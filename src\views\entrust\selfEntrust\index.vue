<template>
  <div class="self-entrust">
    <!-- 自主委托确认弹窗 -->
    <CustomModal
      v-model:open="confirmModalVisible"
      title="自主委托"
      :width="680"
      cancel-text="放弃"
      confirm-text="确认"
      :mask-closable="false"
      @confirm="handleConfirmSelfEntrust"
      @cancel="handleAbandonSelfEntrust"
    >
      <div class="confirm-modal-content">
        <p>我方仅将客户资产信息在我网进行公示，我司不会对客户所发布信息做任何协助</p>
        <p>请确认是否选择自主服务</p>
      </div>
    </CustomModal>

    <!-- 步骤条 -->
    <div class="steps-wrapper">
      <Steps :current="currentStep" :steps="stepsList" @change="handleStepChange" />
    </div>

    <!-- 步骤内容 -->
    <div class="step-content">
      <!-- 第一步：编辑委托信息 -->
      <div v-if="currentStep === 0">
        <Step1
          ref="step1Ref"
          v-model="stepOneData"
          :location-loading="locationLoading"
          :is-edit-mode="isEditMode"
          @area-change="handleAreaChange"
          @get-current-location="getCurrentLocation"
          @service-type-change="handleServiceTypeChange"
        />

        <!-- 操作按钮 -->
        <div class="step-actions">
          <a-button type="primary" size="large" class="next-btn" @click="nextStep"> 下一步 </a-button>
        </div>
      </div>

      <!-- 第二步 -->
      <div v-if="currentStep === 1">
        <!-- 发布竞价标的：显示标的信息 -->
        <Step2 v-if="serviceType === 1" ref="step2Ref" v-model="stepTwoData" :service-type="serviceType" :step-one-data="stepOneData" />
        <!-- 其他类型：显示联系信息 -->
        <Step3 v-else ref="step3Ref" v-model="stepThreeData" />

        <!-- 操作按钮 -->
        <div class="step-actions">
          <!-- 编辑模式下根据状态显示不同按钮 -->
          <template v-if="isEditMode">
            <!-- 如果是草稿状态，显示保存草稿和确认发布按钮 -->
            <template v-if="currentStatus === 1">
              <a-button size="large" class="prev-btn" @click="prevStep" v-if="serviceType === 1"> 上一步 </a-button>
              <!-- 保存至草稿按钮暂时隐藏 -->
              <!-- <a-button v-if="serviceType === 1" size="large" class="draft-btn" @click="saveDraft"> 保存至草稿 </a-button>
              <a-button v-else size="large" class="draft-btn" @click="submitForm(1)"> 保存至草稿 </a-button> -->
              <a-button v-if="serviceType === 1" type="primary" size="large" class="next-btn" @click="nextStep"> 下一步 </a-button>
              <a-button v-else type="primary" size="large" class="submit-btn" :loading="submitting" @click="submitForm(2)"> 确认发布 </a-button>
            </template>
            <!-- 如果不是草稿状态，显示保存修改按钮 -->
            <template v-else>
              <a-button v-if="serviceType === 1" size="large" class="prev-btn" @click="prevStep"> 上一步 </a-button>
              <a-button v-if="serviceType === 1" type="primary" size="large" class="next-btn" @click="nextStep"> 下一步 </a-button>
              <a-button v-else type="primary" size="large" class="submit-btn" :loading="submitting" @click="submitForm(2)"> 保存修改 </a-button>
            </template>
          </template>
          <!-- 新增模式下显示原有按钮 -->
          <template v-else>
            <a-button size="large" class="prev-btn" @click="prevStep" v-if="serviceType === 1"> 上一步 </a-button>
            <!-- 保存至草稿按钮暂时隐藏 -->
            <!-- <a-button v-if="serviceType === 1" size="large" class="draft-btn" @click="saveDraft"> 保存至草稿 </a-button>
            <a-button v-else size="large" class="draft-btn" @click="submitForm(1)"> 保存至草稿 </a-button> -->
            <a-button v-if="serviceType === 1" type="primary" size="large" class="next-btn" @click="nextStep"> 下一步 </a-button>
            <a-button v-else type="primary" size="large" class="submit-btn" :loading="submitting" @click="submitForm(2)"> 确认发布 </a-button>
          </template>
        </div>
      </div>

      <!-- 第三步：联系信息（仅发布竞价标的时显示） -->
      <div v-if="currentStep === 2 && serviceType === 1">
        <Step3 ref="step3Ref" v-model="stepThreeData" />

        <!-- 操作按钮 -->
        <div class="step-actions">
          <!-- 编辑模式下根据状态显示不同按钮 -->
          <template v-if="isEditMode">
            <!-- 如果是草稿状态，显示保存草稿和确认发布按钮 -->
            <template v-if="currentStatus === 1">
              <a-button size="large" class="prev-btn" @click="prevStep"> 上一步 </a-button>
              <a-button size="large" class="draft-btn" @click="submitForm(1)"> 保存至草稿 </a-button>
              <a-button type="primary" size="large" class="submit-btn" :loading="submitting" @click="submitForm(2)"> 确认发布 </a-button>
            </template>
            <!-- 如果不是草稿状态，显示保存修改按钮 -->
            <template v-else>
              <a-button type="primary" size="large" class="submit-btn" :loading="submitting" @click="submitForm(2)"> 保存修改 </a-button>
            </template>
          </template>
          <!-- 新增模式下显示原有按钮 -->
          <template v-else>
            <a-button size="large" class="prev-btn" @click="prevStep"> 上一步 </a-button>
            <a-button size="large" class="draft-btn" @click="submitForm(1)"> 保存至草稿 </a-button>
            <a-button type="primary" size="large" class="submit-btn" :loading="submitting" @click="submitForm(2)"> 确认发布 </a-button>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref, computed, onMounted, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import { useRouter, useRoute } from 'vue-router';
  import Steps from '@/components/Steps/index.vue';
  import Step1 from './components/Step1.vue';
  import Step2 from './components/Step2.vue';
  import Step3 from './components/Step3.vue';
  import { CustomModal } from '/@/components/Modal';
  // 引入拍卖相关API接口和类型定义
  import { addAuctionSelfItemTemp, addSelfAssetEntrust, addSelfProcurement, getCompanyList } from '@/api/entrust/auction';
  import type { AddAuctionItemTempParams, HgyEntrustOrder, HgyAuctionItemTemp, HgyAuction } from '@/api/entrust/types';
  import type { CompanyInfo, CompanyListResponse } from '@/api/entrust/auction';
  // 引入委托详情API
  import { queryEntrustById } from '@/api/orderManage/autonomouslyDispose';
  import type { EntrustDisposeRecord } from '@/api/orderManage/autonomouslyDispose';
  // 引入自主竞价详情API
  import { queryOrderAuctionItemById } from '@/api/orderManage/autonomouslyBidding';
  import type { EntrustBiddingRecord } from '@/api/orderManage/autonomouslyBidding';

  // 组件引用
  const step1Ref = ref<InstanceType<typeof Step1>>();
  const step2Ref = ref<InstanceType<typeof Step2>>();
  const step3Ref = ref<InstanceType<typeof Step3>>();

  // 路由实例
  const router = useRouter();
  const route = useRoute();

  // 自主委托确认弹窗状态
  const confirmModalVisible = ref(false);

  // 当前步骤
  const currentStep = ref(0);

  // 服务类型 (1-发布竞价标的 2-发布资产处置 3-发布采购信息)
  const serviceType = ref(1);

  // 提交状态
  const submitting = ref(false);

  // 定位加载状态
  const locationLoading = ref(false);

  // 编辑状态
  const isEditMode = ref(false);
  const editId = ref<string>('');
  const auctionId = ref<string>('');
  const itemTempId = ref<string>('');
  const currentStatus = ref<number>(2); // 当前记录状态：1-草稿，2-已提交

  // 企业信息列表
  const companyList = ref<CompanyInfo[]>([]);
  // 企业信息加载状态
  const companyLoading = ref(false);

  // 动态步骤列表
  const stepsList = computed(() => {
    if (serviceType.value === 1) {
      // 发布竞价标的：三步
      return [
        {
          title: '新建拍卖会',
          description: '创建拍卖会基本信息',
        },
        {
          title: '新建标的',
          description: '添加拍卖标的信息',
        },
        {
          title: '发布委托信息',
          description: '填写联系人信息并发布',
        },
      ];
    } else {
      // 发布资产处置和采购信息：两步
      return [
        {
          title: '编辑委托信息',
          description: '填写委托的基本信息',
        },
        {
          title: '发布委托信息',
          description: '填写联系人信息并发布',
        },
      ];
    }
  });

  // 第一步表单数据
  let stepOneData = reactive({
    // 服务类型
    serviceType: 1,
    // 拍卖会信息（仅发布竞价标的显示）
    auctionInfo: {
      auctionName: '', // 拍卖会名称
      entrustCompany: '', // 委托单位
      auctionForm: 1, // 拍卖形式(1-同步 2-线上)
      registerEndTime: '', // 报名截止时间
      startTime: '', // 开拍时间
      endType: 1, // 结束方式(1-手动 2-自动)
      auctionType: 1, // 拍卖方式(1-正常 2-减价 3-盲拍 4-混合式报价 5-盲拍指定版)
      auctionNotice: [], // 拍卖公告文件
      auctionRules: [], // 拍卖须知文件
      importantStatement: [], // 重要声明文件
      noticeContent: '', // 拍卖公告内容
      rulesContent: '', // 拍卖须知内容
      statementContent: '', // 重要声明内容
    },
    // 委托信息
    entrustInfo: {
      title: '', // 委托单位（显示企业名称）
      type: '', // 受委托单位（显示企业名称）
      description: '', // 委托描述
      noticeName: '', // 公告名称（仅用于采购信息）
    },
    // 基本信息
    basicInfo: {
      // 发布资产处置
      entrustCompanyId: '', // 处置单位ID
      assetName: '', // 资产名称
      assetNo: '', // 资产编号
      assetType: [] as string[], // 资产类型（级联选择器数组格式）
      quantity: '', // 资产数量
      unit: '', // 计量单位
      quantityFlag: 0, // 是否展示实际数量 0-否 1-是
      serviceLife: 0, // 使用年限
      depreciationDegree: 1, // 新旧程度(1-九成新 2-八成新...)
      currentStatus: 1, // 当前状态(1-在用 2-闲置 3-报废)
      appraisalValue: 0, // 评估价值
      disposalPrice: 0, // 处置底价
      disposalStartTime: '', // 处置开始时间
      disposalEndTime: '', // 处置结束时间
      paymentMethod: 1, // 付款方式(1-全款 2-分期)
      isTaxIncluded: '0', // 是否含税(0:表示不含税,other：表示含税率)
      taxRate: 0, // 税点
      servicePayType: 1, // 支付方式 1-买方支付 2-卖方支付，默认卖方支付
    },
    // 存放位置
    location: {
      province: '110000', // 省份
      city: '110100', // 城市
      area: '110101', // 区域
      detailAddress: '', // 详细地址
      coordinates: {
        // 坐标信息
        latitude: '',
        longitude: '',
      },
    },
    // 资料上传
    materials: {
      images: [] as any[] | string, // 资产图片
      attachments: [] as any[] | string, // 附件上传
      specialNote: '', // 特殊说明
    },
    /*
      原本other中的几个字段是放在根目录下的，现在报过了一层other，
      因为根目录下传递到子组件解构赋值导致根目录下的变量失去了响应式，
      包裹一层就不会有这个问题，其他的解决方案
      （1.不使用解构赋值，2.使用toRef和toRefs(这种方法会引起类型报错，
      需要调整被代理的对象中所有数据的类型，所以我现在使用了直接把根目录下的数据包裹一层对象的形式解决)）
    */
    other: {
      // 直接字段（与Step1组件Props接口匹配）
      images: [] as any[] | string, // 资产图片
      attachments: [] as any[] | string, // 附件上传
      entrustDocument: [], // 委托单上传
      specialNote: '', // 特殊说明
      coverImage: '', // 封面图片（仅发布竞价标的时使用）
    },
    // 附件列表（用于回显）
    hgyAttachmentList: [] as any[],
  });

  // 第二步表单数据
  let stepTwoData = reactive({
    // 标的列表（仅发布竞价标的显示）
    auctionItems: [
      {
        id: '', // 标的ID
        auctionName: '', // 关联拍卖会
        itemTitle: '', // 标的标题
        itemType: 1, // 标的分类(1-物资/设备 2-机动车 3-房产 4-土地 5-其他)
        province: '110000', // 省份
        city: '110100', // 城市
        district: '110101', // 区县
        address: '', // 详细地址
        startPrice: 0, // 起拍价
        appraisalPrice: 0, // 评估价格
        reservePrice: 0, // 保留价
        bidIncrement: 0, // 加价幅度
        deposit: 0, // 保证金
        quantity: 1, // 标的数量
        unit: '', // 标的单位
        quantityFlag: 0, // 是否展示实际数量 0-否 1-是
        showCommission: '', // 展示佣金
        auctionMode: 1, // 拍卖方式(1-总价 2-单价)
        freeBidTime: '', // 自由竞价时间
        timedBidTime: '', // 限时竞价时间
        description: '', // 拍卖公告
        coverImage: '', // 封面图片
        itemImages: [], // 标的图片
      },
    ],
  });

  // 第三步表单数据（联系人信息）
  let stepThreeData = reactive({
    contact: {
      contactName: '', // 联系人姓名
      contactPhone: '', // 联系电话
    },
  });

  watch(
    stepOneData,
    (newVal) => {
      console.log('stepOneData变化:', newVal);
    },
    { deep: true }
  );

  // 处理步骤变化
  const handleStepChange = (step: number) => {
    // 只允许点击当前步骤之前的步骤返回，当前步骤之后的步骤不能通过点击步骤条跳转
    if (step < currentStep.value) {
      currentStep.value = step;
    }
  };

  // 省市区变化处理
  const handleAreaChange = (value: any) => {
    console.log('省市区变化:', value);
  };

  // 获取当前位置
  const getCurrentLocation = () => {
    locationLoading.value = true;

    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          // 模拟根据经纬度获取地址信息
          setTimeout(() => {
            stepOneData.location.detailAddress = '模拟获取的详细地址：北京市海淀区中关村大街1号';
            locationLoading.value = false;
            message.success('位置获取成功');
          }, 1000);
        },
        (error) => {
          locationLoading.value = false;
          message.error('位置获取失败，请手动输入地址');
        }
      );
    } else {
      locationLoading.value = false;
      message.error('浏览器不支持地理位置获取');
    }
  };

  // 服务类型变化处理
  const handleServiceTypeChange = (value: number) => {
    serviceType.value = value;
    // 重置步骤
    currentStep.value = 0;
    // 更新stepOneData中的服务类型，确保数据同步
    stepOneData.serviceType = value;
    // 根据服务类型更新数据
    if (value === 1) {
      // 发布竞价标的 - 数据已经在stepOneData中统一管理
    }
  };

  // 获取企业信息列表
  const fetchCompanyList = async () => {
    try {
      companyLoading.value = true;
      const result: CompanyListResponse = await getCompanyList();
      console.log('获取企业信息:', result);

      if (result && Array.isArray(result)) {
        companyList.value = result;

        // 自动填充委托单位和受委托单位名称（仅在非编辑模式下）
        if (!isEditMode.value) {
          const entrustCompany = result.find((item) => item.value !== '1003'); // 委托企业
          const entrusteeCompany = result.find((item) => item.value === '1003'); // 受委托企业

          if (entrustCompany) {
            stepOneData.entrustInfo.title = entrustCompany.text;
            stepOneData.auctionInfo.entrustCompany = entrustCompany.text;
          }
          if (entrusteeCompany) {
            stepOneData.entrustInfo.type = entrusteeCompany.text;
          }
        }
      }
    } catch (error) {
      console.error('获取企业信息失败:', error);
    } finally {
      companyLoading.value = false;
    }
  };

  // 自主委托确认弹窗事件处理
  const handleConfirmSelfEntrust = () => {
    confirmModalVisible.value = false;
  };

  const handleAbandonSelfEntrust = () => {
    confirmModalVisible.value = false;
    // 跳转回增值委托页面
    router.push('/entrust/appreciationEntrust');
  };

  // 获取委托详情数据
  const fetchEntrustDetail = async (id: string) => {
    try {
      let result;

      // 根据服务类型调用不同的接口
      if (serviceType.value === 1) {
        // 服务类型为1时，调用自主竞价详情接口
        result = await queryOrderAuctionItemById(id);
        console.log('获取到的自主竞价详情:', result);

        // 处理自主竞价数据回显
        if (result) {
          await handleAuctionDataDisplay(result);
          return;
        }
      } else {
        // 服务类型为2时，调用原有的资产处置详情接口
        result = await queryEntrustById(id);
        console.log('获取到的委托详情:', result);
      }

      // 回显数据到表单（原有逻辑，用于服务类型为2的情况）
      if (result) {
        const { hgyAssetEntrust, hgyEntrustOrder, hgyAttachmentList } = result;

        // 回显基本信息
        if (hgyAssetEntrust) {
          stepOneData.basicInfo.assetName = hgyAssetEntrust.assetName || '';
          stepOneData.basicInfo.assetNo = hgyAssetEntrust.assetNo || '';
          // 处理资产类型回显，优先使用分级字段，如果没有则使用旧的assetType字段
          if (hgyAssetEntrust.assetTypeOne || hgyAssetEntrust.assetTypeTwo || hgyAssetEntrust.assetTypeThree) {
            stepOneData.basicInfo.assetType = (
              [hgyAssetEntrust.assetTypeOne, hgyAssetEntrust.assetTypeTwo, hgyAssetEntrust.assetTypeThree] as string[]
            ).filter(Boolean); // 过滤掉空值
          } else if (hgyAssetEntrust.assetType) {
            // 兼容旧版本，如果是字符串则转换为数组
            stepOneData.basicInfo.assetType =
              typeof hgyAssetEntrust.assetType === 'string' ? hgyAssetEntrust.assetType.split(',').filter(Boolean) : [];
          } else {
            stepOneData.basicInfo.assetType = [];
          }
          stepOneData.basicInfo.quantity = hgyAssetEntrust.quantity?.toString() || '';
          stepOneData.basicInfo.unit = hgyAssetEntrust.unit || '';
          stepOneData.basicInfo.quantityFlag = hgyAssetEntrust.quantityFlag || 0; // 是否展示实际数量
          stepOneData.basicInfo.serviceLife = hgyAssetEntrust.serviceLife || 0;
          stepOneData.basicInfo.depreciationDegree = hgyAssetEntrust.depreciationDegree || 1;
          stepOneData.basicInfo.currentStatus = hgyAssetEntrust.currentStatus || 1;
          stepOneData.basicInfo.appraisalValue = hgyAssetEntrust.appraisalValue || 0;
          stepOneData.basicInfo.disposalPrice = hgyAssetEntrust.disposalPrice || 0;
          stepOneData.basicInfo.disposalStartTime = hgyAssetEntrust.disposalStartTime || '';
          stepOneData.basicInfo.disposalEndTime = hgyAssetEntrust.disposalEndTime || '';
          stepOneData.basicInfo.paymentMethod = hgyAssetEntrust.paymentMethod || 1;
          stepOneData.basicInfo.isTaxIncluded = hgyAssetEntrust.isTaxIncluded?.toString() || '0';
          stepOneData.basicInfo.servicePayType = hgyAssetEntrust.servicePayType ?? 1; // 支付方式，默认买方支付

          // 回显地址信息
          stepOneData.location.province = hgyAssetEntrust.provinceCode || '';
          stepOneData.location.city = hgyAssetEntrust.cityCode || '';
          stepOneData.location.area = hgyAssetEntrust.districtCode || '';
          stepOneData.location.detailAddress = hgyAssetEntrust.address || '';

          // 回显特殊说明
          stepOneData.materials.specialNote = hgyAssetEntrust.specialNotes || '';
          stepOneData.other.specialNote = hgyAssetEntrust.specialNotes || '';

          // 回显封面图片
          stepOneData.other.coverImage = hgyAssetEntrust.coverImage || '';
        }

        // 回显委托信息
        if (hgyEntrustOrder) {
          stepOneData.entrustInfo.title = hgyEntrustOrder.entrustCompanyName || '';
          stepOneData.entrustInfo.type = hgyEntrustOrder.onEntrustCompanyName || '';
          stepOneData.basicInfo.entrustCompanyId = hgyEntrustOrder.entrustCompanyId?.toString() || '';

          // 设置当前记录状态
          currentStatus.value = hgyEntrustOrder.status || 2;

          // 回显联系人信息
          stepThreeData.contact.contactName = hgyEntrustOrder.relationUser || '';
          stepThreeData.contact.contactPhone = hgyEntrustOrder.relationPhone || '';

          console.log('回显联系人信息:', {
            contactName: hgyEntrustOrder.relationUser,
            contactPhone: hgyEntrustOrder.relationPhone,
          });
        }

        // 在编辑模式下，如果委托单位或受委托单位为空，则从企业信息列表中自动填充
        if (isEditMode.value && companyList.value.length > 0) {
          if (!stepOneData.entrustInfo.title) {
            const entrustCompany = companyList.value.find((item) => item.value !== '1003'); // 委托企业
            if (entrustCompany) {
              stepOneData.entrustInfo.title = entrustCompany.text;
              stepOneData.auctionInfo.entrustCompany = entrustCompany.text;
              stepOneData.basicInfo.entrustCompanyId = entrustCompany.value; // 同时填充处置单位ID
            }
          }

          if (!stepOneData.entrustInfo.type) {
            const entrusteeCompany = companyList.value.find((item) => item.value === '1003'); // 受委托企业
            if (entrusteeCompany) {
              stepOneData.entrustInfo.type = entrusteeCompany.text;
            }
          }
        }

        // 回显附件信息
        if (hgyAttachmentList && Array.isArray(hgyAttachmentList) && hgyAttachmentList.length > 0) {
          stepOneData.hgyAttachmentList = hgyAttachmentList;
        }
      }
    } catch (error) {
      console.error('获取委托详情失败:', error);
    }
  };

  // 处理自主竞价数据回显（服务类型为1时使用）
  const handleAuctionDataDisplay = async (result: any) => {
    try {
      // 根据JJapi.txt说明，数据结构包含hgyEntrustOrder、hgyAuctionItemTemp、hgyAuction三部分
      if (result) {
        const { hgyEntrustOrder, hgyAuctionItemTemp, hgyAuction } = result;
        auctionId.value = hgyAuction?.id || '';
        itemTempId.value = hgyAuctionItemTemp?.id || '';

        // 回显委托单信息
        if (hgyEntrustOrder) {
          stepOneData.entrustInfo.title = hgyEntrustOrder.entrustCompanyName || '';
          stepOneData.entrustInfo.type = hgyEntrustOrder.onEntrustCompanyName || '';
          stepOneData.basicInfo.entrustCompanyId = hgyEntrustOrder.entrustCompanyId?.toString() || '';

          // 设置当前记录状态
          currentStatus.value = hgyEntrustOrder.status || 2;

          // 回显联系人信息
          stepThreeData.contact.contactName = hgyEntrustOrder.relationUser || '';
          stepThreeData.contact.contactPhone = hgyEntrustOrder.relationPhone || '';
        }

        // 在编辑模式下，如果委托单位或受委托单位为空，则从企业信息列表中自动填充
        if (isEditMode.value && companyList.value.length > 0) {
          if (!stepOneData.entrustInfo.title) {
            const entrustCompany = companyList.value.find((item) => item.value !== '1003'); // 委托企业
            if (entrustCompany) {
              stepOneData.entrustInfo.title = entrustCompany.text;
              stepOneData.auctionInfo.entrustCompany = entrustCompany.text;
              stepOneData.basicInfo.entrustCompanyId = entrustCompany.value; // 同时填充处置单位ID
            }
          }

          if (!stepOneData.entrustInfo.type) {
            const entrusteeCompany = companyList.value.find((item) => item.value === '1003'); // 受委托企业
            if (entrusteeCompany) {
              stepOneData.entrustInfo.type = entrusteeCompany.text;
            }
          }
        }

        // 回显拍卖标的临时信息
        if (hgyAuctionItemTemp) {
          stepOneData.basicInfo.assetName = hgyAuctionItemTemp.itemName || '';
          stepOneData.basicInfo.quantity = hgyAuctionItemTemp.quantity?.toString() || '';
          stepOneData.basicInfo.unit = hgyAuctionItemTemp.unit || '';

          // 回显地址信息
          stepOneData.location.province = hgyAuctionItemTemp.province || '';
          stepOneData.location.city = hgyAuctionItemTemp.city || '';
          stepOneData.location.area = hgyAuctionItemTemp.district || '';
          stepOneData.location.detailAddress = hgyAuctionItemTemp.address || '';

          // 回显特殊说明
          stepOneData.materials.specialNote = hgyAuctionItemTemp.specialNotes || '';
          stepOneData.other.specialNote = hgyAuctionItemTemp.specialNotes || '';

          // 回显保留价信息
          if (hgyAuctionItemTemp.hasReservePrice === 1) {
            stepOneData.basicInfo.disposalPrice = hgyAuctionItemTemp.reservePrice || 0;
          }

          // 回显附件信息
          if (hgyAuctionItemTemp.attachmentList && Array.isArray(hgyAuctionItemTemp.attachmentList) && hgyAuctionItemTemp.attachmentList.length > 0) {
            stepOneData.hgyAttachmentList = hgyAuctionItemTemp.attachmentList;
          }
        }

        // 回显拍卖信息（自主委托特有）
        if (hgyAuction) {
          // 回显拍卖会信息
          stepOneData.auctionInfo.auctionName = hgyAuction.auctionName || '';
          stepOneData.auctionInfo.entrustCompany = hgyAuction.entrustCompanyName || '';
          stepOneData.auctionInfo.auctionForm = hgyAuction.auctionForm || 1;
          stepOneData.auctionInfo.registerEndTime = hgyAuction.registerEndTime || '';
          stepOneData.auctionInfo.startTime = hgyAuction.startTime || '';
          stepOneData.auctionInfo.endType = hgyAuction.endType || 1;
          stepOneData.auctionInfo.auctionType = hgyAuction.auctionType || 1;
          stepOneData.auctionInfo.noticeContent = hgyAuction.auctionNotice || '';
          stepOneData.auctionInfo.rulesContent = hgyAuction.auctionNotes || '';
          stepOneData.auctionInfo.statementContent = hgyAuction.importantNotice || '';
          stepOneData.other.coverImage = hgyAuction.coverImage || '';

          // 回显标的列表到stepTwoData
          if (hgyAuction.hgyAuctionItemList && hgyAuction.hgyAuctionItemList.length > 0) {
            stepTwoData.auctionItems = hgyAuction.hgyAuctionItemList.map((auctionItem) => ({
              id: auctionItem.id,
              auctionName: hgyAuction.auctionName || '',
              itemTitle: auctionItem.itemTitle || auctionItem.itemName || '',
              itemType: auctionItem.itemType || 1,
              province: auctionItem.province || '110000',
              city: auctionItem.city || '110100',
              district: auctionItem.district || '110101',
              address: auctionItem.address || '',
              startPrice: auctionItem.startPrice || 0,
              appraisalPrice: auctionItem.appraisalPrice || 0,
              reservePrice: auctionItem.reservePrice || 0,
              bidIncrement: auctionItem.bidIncrement || 0, // 加价幅度
              deposit: auctionItem.deposit || 0,
              quantity: parseInt(auctionItem.quantity || '1'),
              unit: auctionItem.unit || '件',
              quantityFlag: auctionItem.quantityFlag || 0, // 是否展示实际数量
              showCommission: auctionItem.showCommission || '',
              auctionMode: auctionItem.auctionMode || 1,
              freeBidTime: auctionItem.freeBidTime || '',
              timedBidTime: auctionItem.timedBidTime || '',
              description: auctionItem.description || '',
              coverImage: auctionItem.coverImage || '',
            }));
          }
        }
      }
    } catch (error) {
      console.error('自主竞价数据回显失败:', error);
    }
  };

  // 组件挂载时获取企业信息和处理编辑模式
  onMounted(async () => {
    // 先获取企业信息
    await fetchCompanyList();

    // 检查是否为编辑模式
    const id = route.query.id as string;
    const serviceTypeParam = route.query.serviceType as string;

    if (id) {
      isEditMode.value = true;
      editId.value = id;

      // 设置服务类型
      if (serviceTypeParam) {
        const serviceTypeValue = parseInt(serviceTypeParam);
        serviceType.value = serviceTypeValue;
        stepOneData.serviceType = serviceTypeValue;
      }

      // 如果是编辑模式，不显示确认弹窗
      confirmModalVisible.value = false;

      // 获取委托详情（在企业信息获取完成后）
      fetchEntrustDetail(id);
    } else {
      confirmModalVisible.value = true;
    }
  });

  // 下一步
  const nextStep = async () => {
    // 验证当前步骤的表单数据
    if (currentStep.value === 0) {
      console.log('验证当前步骤的表单数据', stepOneData);
      // 第一步：验证基本信息
      const isValid = await step1Ref.value?.validateForm();
      if (!isValid) {
        return;
      }
    } else if (currentStep.value === 1) {
      // 第二步：根据服务类型验证对应组件
      if (serviceType.value === 1) {
        // 发布竞价标的：验证标的信息
        const isValid = await step2Ref.value?.validateForm();
        if (!isValid) {
          return;
        }
      } else {
        // 其他类型：验证联系信息
        const isValid = await step3Ref.value?.validateForm();
        if (!isValid) {
          return;
        }
      }
    }
    console.log('验证通过，可以跳转到下一步', stepOneData);

    if (currentStep.value < stepsList.value.length - 1) {
      currentStep.value++;
    }
  };

  // 上一步
  const prevStep = () => {
    if (currentStep.value > 0) {
      currentStep.value--;
    }
  };

  // 保存草稿
  const saveDraft = () => {
    submitForm(1);
  };

  // 根据文件扩展名判断文件类型的函数
  const getFileTypeByExtension = (filePath: string): string => {
    if (!filePath) return 'other';

    const extension = filePath.toLowerCase().split('.').pop();

    switch (extension) {
      case 'pdf':
        return 'pdf';
      case 'ppt':
      case 'pptx':
        return 'ppt';
      case 'xls':
      case 'xlsx':
        return 'excel';
      case 'doc':
      case 'docx':
        return 'doc';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return 'image';
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
      case 'flv':
        return 'video';
      case 'mp3':
      case 'wav':
      case 'flac':
      case 'aac':
        return 'mp3';
      case 'zip':
      case 'rar':
      case '7z':
      case 'tar':
      case 'gz':
        return 'zip';
      default:
        return 'other';
    }
  };

  // 提交表单
  const submitForm = async (status: number) => {
    // 验证最后一步表单数据
    if (serviceType.value === 1) {
      // 发布竞价标的：验证第三步
      const isStep3Valid = await step3Ref.value?.validateForm();
      if (!isStep3Valid) {
        return;
      }
    } else {
      // 发布资产处置或采购信息：验证第二步（实际是step3组件）
      const isStep3Valid = await step3Ref.value?.validateForm();
      if (!isStep3Valid) {
        return;
      }
    }

    submitting.value = true;

    try {
      // 根据企业名称获取对应的ID值
      const getCompanyIdByName = (companyName: string): string => {
        const company = companyList.value.find((item) => item.text === companyName);
        return company ? company.value : '';
      };

      // 获取封面图片路径的函数
      const getCoverImage = (stepData: any): string => {
        // 优先从 other.coverImage 字段获取封面图片
        if (stepData.other && stepData.other.coverImage) {
          try {
            const coverImage = typeof stepData.other.coverImage === 'string' ? JSON.parse(stepData.other.coverImage) : stepData.other.coverImage;
            if (Array.isArray(coverImage) && coverImage.length > 0) {
              return coverImage[0].filePath || '';
            } else if (typeof coverImage === 'string') {
              return coverImage;
            }
          } catch (e) {
            console.warn('解析封面图片数据失败:', e);
          }
        }

        // 如果没有专门的封面图片，则从 materials.images 中获取第一张图片作为封面图片
        if (stepData.materials && stepData.materials.images) {
          try {
            const images = typeof stepData.materials.images === 'string' ? JSON.parse(stepData.materials.images) : stepData.materials.images;
            if (Array.isArray(images) && images.length > 0) {
              return images[0].filePath || '';
            }
          } catch (e) {
            console.warn('解析图片数据失败:', e);
          }
        }
        return '';
      };

      // 构造附件列表的通用函数
      const buildAttachmentList = (bizType: string, materials: any) => {
        console.log('构造附件列表:', materials);
        const attachmentList: any[] = [];

        // 处理图片附件
        if (materials.images) {
          try {
            const images = typeof materials.images === 'string' ? JSON.parse(materials.images) : materials.images;
            if (Array.isArray(images)) {
              attachmentList.push(
                ...images.map((file: any) => ({
                  bizType,
                  fileName: file.fileName,
                  filePath: file.filePath,
                  fileSize: file.fileSize,
                  fileType: getFileTypeByExtension(file.filePath),
                }))
              );
            }
          } catch (e) {
            console.warn('解析图片附件数据失败:', e);
          }
        }

        // 处理其他附件
        if (materials.attachments) {
          try {
            const attachments = typeof materials.attachments === 'string' ? JSON.parse(materials.attachments) : materials.attachments;
            if (Array.isArray(attachments)) {
              attachmentList.push(
                ...attachments.map((file: any) => ({
                  bizType,
                  fileName: file.fileName,
                  filePath: file.filePath,
                  fileSize: file.fileSize,
                  fileType: getFileTypeByExtension(file.filePath),
                }))
              );
            }
          } catch (e) {
            console.warn('解析其他附件数据失败:', e);
          }
        }

        return attachmentList;
      };

      let result: any;

      if (serviceType.value === 1) {
        // 发布竞价标的 - 构建符合API要求的数据结构
        const contactData = {
          contactName: stepThreeData.contact.contactName,
          contactPhone: stepThreeData.contact.contactPhone,
        };

        // 构造拍卖会数据 - 使用 stepOneData 中的实际表单数据
        const auctionInfo = stepOneData.auctionInfo;
        const hgyAuction = {
          id: isEditMode.value ? auctionId.value : undefined, // 修改时传id，新增时不传
          auctionName: auctionInfo.auctionName,
          auctionType: auctionInfo.auctionType,
          entrustCompanyId: Number(getCompanyIdByName(auctionInfo.entrustCompany)),
          entrustCompanyName: auctionInfo.entrustCompany,
          registerEndTime: auctionInfo.registerEndTime,
          startTime: auctionInfo.startTime,
          endType: auctionInfo.endType,
          auctionForm: auctionInfo.auctionForm,
          coverImage: stepOneData.other.coverImage,
          auctionNotice: auctionInfo.noticeContent,
          auctionNotes: auctionInfo.rulesContent,
          importantNotice: auctionInfo.statementContent,
          hgyAuctionItemList: stepTwoData.auctionItems.map((item, index) => ({
            id: isEditMode.value ? item.id : undefined, // 修改时传id，新增时不传
            itemNo: (index + 1).toString(),
            itemType: item.itemType,
            itemName: item.itemTitle,
            itemTitle: item.itemTitle,
            province: item.province,
            city: item.city,
            district: item.district, // 注意这里是 district 不是 area
            address: item.address, // 注意这里是 address 不是 detailAddress
            coverImage: item.coverImage,
            startPrice: item.startPrice ? Number(item.startPrice) : 0,
            appraisalPrice: item.appraisalPrice ? Number(item.appraisalPrice) : 0,
            hasReservePrice: item.reservePrice && item.reservePrice > 0 ? 1 : 0, // 根据是否有保留价判断
            reservePrice: item.reservePrice ? Number(item.reservePrice) : 0,
            bidIncrement: item.bidIncrement ? Number(item.bidIncrement) : 0, // 加价幅度
            deposit: item.deposit ? Number(item.deposit) : 0,
            quantity: String(item.quantity || ''), // 转换为字符串类型
            unit: item.unit,
            quantityFlag: item.quantityFlag, // 是否展示实际数量 0-否 1-是
            showCommission: item.showCommission,
            auctionMode: item.auctionMode,
            freeBidTime: item.freeBidTime,
            timedBidTime: item.timedBidTime,
            description: item.description,
            hgyAttachmentList: buildAttachmentList('ZZJJ', { images: item.itemImages }),
          })),
          hgyAttachmentList: buildAttachmentList('ZZJJ', stepOneData.materials),
        };

        const apiData: AddAuctionItemTempParams = {
          hgyEntrustOrder: {
            id: isEditMode.value ? editId.value : undefined, // 修改时传id，新增时不传
            entrustType: 2, // 自主委托
            serviceType: 1, // 发布竞价标的
            status: status,
            entrustCompanyId: getCompanyIdByName(stepOneData.auctionInfo.entrustCompany),
            entrustCompanyName: stepOneData.auctionInfo.entrustCompany,
            relationUser: contactData.contactName,
            relationPhone: contactData.contactPhone,
          },
          hgyAuctionItemTemp: {
            id: isEditMode.value ? itemTempId.value : undefined, // 修改时传id，新增时不传
            itemName: '', // 标的名称，这里需要从实际数据中获取
            quantity: '', // 标的数量
            auctionDate: stepOneData.auctionInfo.startTime, // 拍卖日期
            hasReservePrice: 0, // 是否设置保留价
            province: '', // 省份
            city: '', // 城市
            district: '', // 区县
            address: '', // 详细地址
            specialNotes: '', // 特殊说明
            attachmentList: [], // 附件列表
          },
          hgyAuction,
        };
        result = await addAuctionSelfItemTemp(apiData);
      } else if (serviceType.value === 2) {
        // 发布资产处置
        const contactData = {
          contactName: stepThreeData.contact.contactName,
          contactPhone: stepThreeData.contact.contactPhone,
        };
        const basicData = stepOneData.basicInfo;

        const apiData = {
          id: isEditMode.value ? editId.value : undefined, // 修改时传id，新增时不传
          entrustType: 2, // 委托类型(1-增值 2-自主)
          serviceType: 2, // 服务类型(2-资产处置)
          status: status, // 状态(1-草稿 2-提交)
          entrustCompanyId: getCompanyIdByName(stepOneData.entrustInfo.title),
          assetName: basicData.assetName,
          assetNo: basicData.assetNo,
          // 将数组格式的资产类型转换为分级字段
          assetTypeOne: Array.isArray(basicData.assetType) && basicData.assetType.length > 0 ? basicData.assetType[0] : '',
          assetTypeTwo: Array.isArray(basicData.assetType) && basicData.assetType.length > 1 ? basicData.assetType[1] : '',
          assetTypeThree: Array.isArray(basicData.assetType) && basicData.assetType.length > 2 ? basicData.assetType[2] : '',
          quantity: basicData.quantity,
          unit: basicData.unit,
          quantityFlag: basicData.quantityFlag, // 是否展示实际数量 0-否 1-是
          servicePayType: basicData.servicePayType, // 支付方式 1-买方支付 2-卖方支付
          serviceLife: Number(basicData.serviceLife),
          depreciationDegree: basicData.depreciationDegree,
          currentStatus: basicData.currentStatus,
          appraisalValue: Number(basicData.appraisalValue),
          disposalPrice: Number(basicData.disposalPrice),
          disposalStartTime: basicData.disposalStartTime,
          disposalEndTime: basicData.disposalEndTime,
          paymentMethod: basicData.paymentMethod,
          isTaxIncluded: basicData.isTaxIncluded,
          provinceCode: stepOneData.location.province,
          cityCode: stepOneData.location.city,
          districtCode: stepOneData.location.area,
          address: stepOneData.location.detailAddress,
          specialNotes: stepOneData.materials.specialNote,
          relationUser: contactData.contactName,
          relationPhone: contactData.contactPhone,
          coverImage: getCoverImage(stepOneData), // 封面图片：优先从coverImage字段获取，否则取标的图片集合中的第一项
          attachmentList: buildAttachmentList('ZZCZ', stepOneData.materials),
        };

        console.log('发布资产处置数据:', apiData);
        result = await addSelfAssetEntrust(apiData);
      } else if (serviceType.value === 3) {
        // 发布采购信息
        const contactData = {
          contactName: stepThreeData.contact.contactName,
          contactPhone: stepThreeData.contact.contactPhone,
        };

        const apiData = {
          id: isEditMode.value ? editId.value : undefined, // 修改时传id，新增时不传
          entrustType: 2, // 委托类型(1-增值 2-自主)
          serviceType: 3, // 服务类型(3-采购信息)
          status: status, // 状态(1-草稿 2-提交)
          noticeName: stepOneData.entrustInfo.noticeName,
          provinceCode: stepOneData.location.province,
          cityCode: stepOneData.location.city,
          districtCode: stepOneData.location.area,
          address: stepOneData.location.detailAddress,
          relationUser: contactData.contactName,
          relationPhone: contactData.contactPhone,
          attachmentList: buildAttachmentList('ZZCG', stepOneData.materials),
        };

        console.log('发布采购信息数据:', apiData);
        result = await addSelfProcurement(apiData);
      }

      // 提交成功后的处理
      // 只有在确认发布(status=2)时才跳转，保存至草稿(status=1)不跳转
      if (status === 2) {
        // 根据服务类型跳转到对应的订单页面
        if (serviceType.value === 1) {
          // 发布竞价标的 -> 自主竞价页面
          router.push('/orderManage/autonomouslyBidding');
        } else if (serviceType.value === 2) {
          // 发布资产处置 -> 自主处置页面
          router.push('/orderManage/autonomouslyDispose');
        } else if (serviceType.value === 3) {
          // 发布采购信息 -> 采购信息页面
          router.push('/hgy/entrustService/hgyProcurementList');
        }
      }
    } catch (error) {
      console.error('委托发布失败:', error);
      message.error('操作失败，请重试');
    } finally {
      submitting.value = false;
    }
  };
</script>

<style lang="less" scoped>
  // 主容器样式
  .self-entrust {
    padding: 24px;
    background-color: #fff;
    height: calc(100vh - 168px); // 减去头部和底部的高度
    overflow-y: auto;
    margin: 19px 30px 30px 30px;
    border-radius: 10px;
  }

  // 确认弹窗内容样式
  .confirm-modal-content {
    text-align: center;
    padding: 20px 0;

    p {
      margin: 12px 0;
      font-size: 16px;
      line-height: 1.6;
      color: #333;
    }
  }

  // 步骤条包装器
  .steps-wrapper {
    margin-bottom: 32px;
  }

  // 步骤内容
  .step-content {
    margin: 0 auto; // 居中显示
  }

  // 步骤面板
  .step-panel {
    background-color: #fff;
    border-radius: 8px;
  }

  // 操作按钮区域
  .step-actions {
    display: flex;
    justify-content: center;
    gap: 16px; // 按钮之间的间距
    margin-top: 48px; // 与表单内容的间距
    padding-top: 24px;

    .prev-btn,
    .next-btn,
    .submit-btn,
    .draft-btn {
      width: 434px; // 按钮最小宽度
      height: 48px; // 按钮高度
      font-size: 16px; // 字体大小
      border-radius: 6px; // 圆角
    }

    .next-btn,
    .submit-btn {
      background-color: #004c66; // 主色调
      border-color: #004c66;
      box-shadow: 0 0 0;

      &:hover:not(:disabled) {
        background: rgba(0, 76, 102, 0.9);
      }
    }

    .prev-btn,
    .draft-btn {
      background-color: #fff;
      color: #666;
      border-color: #d9d9d9;

      &:hover {
        color: #004c66;
        border-color: #004c66;
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .step-content {
      max-width: 100%;
      padding: 0 16px;
    }

    .step-panel {
      padding: 24px 16px;
    }
  }

  @media (max-width: 768px) {
    .self-entrust {
      padding: 16px;
    }

    .steps-wrapper {
      padding: 0 8px;
      margin-bottom: 24px;
    }

    .step-content {
      padding: 0 8px;
    }

    .step-panel {
      padding: 16px;
      border-radius: 4px;
    }

    .step-actions {
      flex-direction: column; // 小屏幕下垂直排列
      gap: 12px;
      margin-top: 32px;

      .prev-btn,
      .next-btn,
      .submit-btn,
      .draft-btn {
        width: 100%; // 小屏幕下占满宽度
      }
    }
  }
</style>
