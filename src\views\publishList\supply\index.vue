<template>
  <div class="p-4">
    <BasicTable @register="registerTable" @navigation-change="handleNavigationChange">
      <!-- 图片列 -->
      <template #images="{ record }">
        <div class="image-container">
          <div v-if="getImageList(record).length === 0" class="no-image">
            <span>暂无图片</span>
          </div>
          <div v-else class="image-list">
            <!-- 第一张图片 -->
            <div class="image-item">
              <img :src="getImageList(record)[0]" alt="供应图片" @click="previewImage(getImageList(record), 0)" />
            </div>
            <!-- 第二张图片（如果有） -->
            <div v-if="getImageList(record).length > 1" class="image-item second-image">
              <img :src="getImageList(record)[1]" alt="供应图片" @click="previewImage(getImageList(record), 1)" />
              <!-- 如果超过两张，显示数量蒙层 -->
              <div v-if="getImageList(record).length > 2" class="image-overlay" @click="previewImage(getImageList(record), 1)">
                <span>{{ getImageList(record).length }}张</span>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 审核状态列 -->
      <template #status="{ record }">
        <a-tag :color="getStatusColor(record.status)">
          {{ getStatusText(record.status) }}
        </a-tag>
      </template>

      <!-- 操作栏 -->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>

    <!-- 图片预览 -->
    <a-modal v-model:open="previewVisible" :footer="null" :width="800" title="图片预览">
      <div class="preview-container">
        <img :src="currentPreviewImage" alt="预览图片" class="preview-image" />
        <div v-if="previewImages.length > 1" class="preview-controls">
          <a-button @click="prevImage" :disabled="currentImageIndex === 0">上一张</a-button>
          <span class="image-counter">{{ currentImageIndex + 1 }} / {{ previewImages.length }}</span>
          <a-button @click="nextImage" :disabled="currentImageIndex === previewImages.length - 1">下一张</a-button>
        </div>
      </div>
    </a-modal>

    <!-- 详情查看弹窗 -->
    <DetailViewModal v-model:open="detailVisible" :record="currentRecord" :entrust-type="3" :service-type="4" @close="handleDetailClose" />
  </div>
</template>

<script lang="ts" setup name="SupplyList">
  import { ref, computed } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { ActionItem, BasicColumn } from '/@/components/Table';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { formatToDateTime } from '/@/utils/dateUtil';
  import { querySupplyDemandList, deleteSupplyDemand } from '/@/api/supplyAndDemand/SupplyDemand';
  import { DetailViewModal } from '/@/components/Audit';
  import type { AuditRecord } from '/@/components/Audit/types';

  const { createMessage, createConfirm } = useMessage();

  // 表格列定义
  const columns: BasicColumn[] = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 60,
      customRender: ({ index }) => index + 1,
    },
    {
      title: '图片',
      dataIndex: 'images',
      width: 120,
      slots: { customRender: 'images' },
    },
    {
      title: '供应标题',
      dataIndex: 'infoTitle',
      width: 200,
      ellipsis: true,
    },
    {
      title: '物资类型',
      dataIndex: 'materialTypeName',
      width: 120,
      ellipsis: true,
      customRender: ({ record }) => {
        const materialTypeOne = record.materialTypeOne_dictText || '-';
        const materialTypeTwo = record.materialTypeTwo_dictText || '';
        const materialTypeThree = record.materialTypeThree_dictText || '';
        return [materialTypeOne, materialTypeTwo, materialTypeThree].filter(Boolean).join(' ');
      },
    },
    {
      title: '所在地区',
      dataIndex: 'address',
      width: 180,
      ellipsis: true,
      customRender: ({ record }) => {
        const province = record.province_dictText || '-';
        const city = record.city_dictText || '';
        const district = record.district_dictText || '';
        return [province, city, district].filter(Boolean).join(' ');
      },
    },
    {
      title: '审核状态',
      dataIndex: 'status',
      width: 100,
      slots: { customRender: 'status' },
    },
    {
      title: '发布时间',
      dataIndex: 'createTime',
      width: 150,
      customRender: ({ text }) => {
        return text ? formatToDateTime(text) : '-';
      },
    },
  ];

  // 导航栏配置
  const navigationItems = [
    { key: 'all', label: '全部供应', icon: '' },
    { key: 'pending', label: '待审核', icon: '' },
    { key: 'approved', label: '已通过', icon: '' },
    { key: 'rejected', label: '未通过', icon: '' },
    { key: 'published', label: '已发布', icon: '' },
  ];

  const activeNavigationKey = ref<string | number>('all');

  // 存储当前导航的查询参数
  const currentNavParams = ref<any>({});

  // 图片预览相关
  const previewVisible = ref(false);
  const previewImages = ref<string[]>([]);
  const currentImageIndex = ref(0);

  const currentPreviewImage = computed(() => {
    return previewImages.value[currentImageIndex.value] || '';
  });

  // 详情查看弹窗状态
  const detailVisible = ref(false);
  const currentRecord = ref<AuditRecord | null>(null);

  // 处理导航切换
  function handleNavigationChange(key: string | number) {
    activeNavigationKey.value = key;

    // 根据导航key设置不同的查询参数
    let searchParams = {};
    switch (key) {
      case 'draft':
        searchParams = { status: 1 }; // 草稿
        break;
      case 'pending':
        searchParams = { status: 2 }; // 待审核
        break;
      case 'approved':
        searchParams = { status: 3 }; // 审核通过
        break;
      case 'rejected':
        searchParams = { status: 4 }; // 审核拒绝
        break;
      case 'published':
        searchParams = { status: 5 }; // 已发布
        break;
      default:
        searchParams = {}; // 全部供应，不设置过滤条件
    }

    // 存储导航参数
    currentNavParams.value = searchParams;
    // 重新加载数据
    reload();
  }

  // 自定义API调用函数
  async function customQuerySupplyList(params: any) {
    // 默认参数
    const defaultParams = {
      type: '4', // 供应类型
    };

    // 合并导航参数和搜索表单参数
    const mergedParams = {
      ...params, // 搜索表单参数
      ...defaultParams,
      ...currentNavParams.value, // 添加导航参数
    };

    return querySupplyDemandList(mergedParams);
  }

  // 获取图片列表
  function getImageList(record: any): string[] {
    try {
      // 从附件列表中提取图片
      if (record.attachmentList && Array.isArray(record.attachmentList)) {
        return record.attachmentList
          .filter((item: any) => item.fileType === 'image')
          .map((item: any) => item.filePath)
          .filter(Boolean);
      }
      return [];
    } catch (error) {
      console.error('解析图片列表失败:', error);
      return [];
    }
  }

  // 图片预览
  function previewImage(images: string[], index: number) {
    previewImages.value = images;
    currentImageIndex.value = index;
    previewVisible.value = true;
  }

  // 上一张图片
  function prevImage() {
    if (currentImageIndex.value > 0) {
      currentImageIndex.value--;
    }
  }

  // 下一张图片
  function nextImage() {
    if (currentImageIndex.value < previewImages.value.length - 1) {
      currentImageIndex.value++;
    }
  }

  // 获取状态颜色
  function getStatusColor(status: number): string {
    switch (status) {
      case 1:
        return 'default'; // 草稿
      case 2:
        return 'processing'; // 待审核
      case 3:
        return 'success'; // 审核通过
      case 4:
        return 'error'; // 审核拒绝
      case 5:
        return 'success'; // 已发布
      case 6:
        return 'warning'; // 已成交
      case 7:
        return 'default'; // 已撤拍
      case 8:
        return 'default'; // 已过期
      default:
        return 'default';
    }
  }

  // 获取状态文本
  function getStatusText(status: number): string {
    switch (status) {
      case 1:
        return '草稿';
      case 2:
        return '待审核';
      case 3:
        return '审核通过';
      case 4:
        return '审核拒绝';
      case 5:
        return '已发布';
      case 6:
        return '已成交';
      case 7:
        return '已撤拍';
      case 8:
        return '已过期';
      default:
        return '未知';
    }
  }

  // 浏览详情
  function handleView(record: any) {
    console.log('handleView - record:', record);

    if (!record || !record.id) {
      console.error('handleView - 缺少record或record.id');
      createMessage.error('数据异常，无法查看详情');
      return;
    }

    try {
      // 构造供求审核记录，传递供应需求的ID
      const auditRecord: AuditRecord = {
        id: String(record.entrustOrderId), // 使用供应需求的ID，不是entrustOrderId
        entrustType: 3, // 供求信息类型
        serviceType: 4, // 供应需求服务类型
        status: Number(record.status) || 2,
        projectName: String(record.infoTitle || '供应求购'),
        relationUser: String(record.relationUser || '-'),
        relationPhone: String(record.relationPhone || '-'),
        applicantUser: String(record.createBy || '-'),
        auditUser: String(record.auditUser || '-'),
        submitTime: String(record.createTime || ''),
        auditTime: String(record.auditTime || ''),
      };

      console.log('handleView - auditRecord:', auditRecord);
      currentRecord.value = auditRecord;
      detailVisible.value = true;
    } catch (error) {
      console.error('handleView - 构造审核记录失败:', error);
      createMessage.error('数据处理异常，无法查看详情');
    }
  }

  // 关闭详情弹窗
  function handleDetailClose() {
    detailVisible.value = false;
    currentRecord.value = null;
  }

  // 删除供应需求
  function handleDelete(record: any) {
    createConfirm({
      iconType: 'warning',
      title: '确认删除',
      content: '确定要删除这条供应需求吗？删除后不可恢复。',
      onOk: async () => {
        try {
          await deleteSupplyDemand({ id: record.id });
          createMessage.success('删除成功');
          reload();
        } catch (error) {
          console.error('删除失败:', error);
          createMessage.error('删除失败');
        }
      },
    });
  }

  // 获取表格操作按钮
  function getTableAction(record: any): ActionItem[] {
    return [
      {
        label: '浏览',
        onClick: () => handleView(record),
      },
      {
        label: '删除',
        color: 'error',
        onClick: () => handleDelete(record),
      },
    ];
  }

  // 表格配置
  const [registerTable, { reload }] = useTable({
    api: customQuerySupplyList,
    columns,
    striped: false,
    useSearchForm: true,
    showTableSetting: false,
    bordered: false,
    showIndexColumn: false,
    canResize: true,
    // 导航栏配置
    showNavigation: true,
    navigationItems,
    activeNavigationKey: activeNavigationKey.value,
    inset: true,
    maxHeight: 478,
    actionColumn: {
      width: 150,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: 'right',
    },
    formConfig: {
      labelWidth: 64,
      size: 'large',
      schemas: [
        {
          field: 'infoTitle',
          label: '供应标题',
          component: 'Input',
          componentProps: {
            placeholder: '请输入供应标题',
          },
          colProps: { span: 6 },
        },
        /* {
          field: 'materialTypeName',
          label: '物资类型',
          component: 'Input',
          componentProps: {
            placeholder: '请输入物资类型',
          },
          colProps: { span: 6 },
        },
        {
          field: 'createTimeRange',
          label: '发布时间',
          component: 'RangePicker',
          componentProps: {
            format: 'YYYY-MM-DD',
            placeholder: ['开始时间', '结束时间'],
          },
          colProps: { span: 6 },
        }, */
      ],
    },
  });
</script>

<style lang="less" scoped>
  .p-4 {
    padding: 0;
    :deep(.ant-pagination) {
      margin-bottom: -24px !important;
    }
    :deep(.ant-form) {
      padding: 0;
    }
  }
  .image-container {
    .no-image {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100px;
      height: 60px;
      background-color: #f5f5f5;
      border-radius: 4px;
      color: #999;
      font-size: 12px;
    }

    .image-list {
      display: flex;
      gap: 4px;
    }

    .image-item {
      position: relative;
      width: 48px;
      height: 48px;
      border-radius: 4px;
      overflow: hidden;
      cursor: pointer;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.2s;

        &:hover {
          transform: scale(1.05);
        }
      }

      &.second-image {
        .image-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.6);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 12px;
          font-weight: bold;
        }
      }
    }
  }

  .preview-container {
    text-align: center;

    .preview-image {
      max-width: 100%;
      max-height: 500px;
      object-fit: contain;
    }

    .preview-controls {
      margin-top: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 16px;

      .image-counter {
        color: #666;
        font-size: 14px;
      }
    }
  }

  :deep(.ant-table-title) {
    margin-top: 12px;
    border-top: 1px solid #ddd;
    padding: 0 !important;
    padding-top: 15px !important;
    margin-right: -8px;
  }
  :deep(.ant-form-item-control-input-content) {
    /* display: flex;
    justify-content: end; */
    button {
      margin-right: 0;
      margin-left: 8px;
      box-shadow: 0 0 0 rgba(3, 38, 43, 0.42);
    }
  }
</style>
