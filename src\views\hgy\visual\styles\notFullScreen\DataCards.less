// 非全屏状态下的样式 - 1658px × 821px
.visual-container:not(.fullscreen) .data-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.data-card {
  position: relative;
  height: 100px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(18, 230, 219, 0.3);
  }
}

.card-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/left-bg_1754987425208.png') no-repeat center center;
  background-size: cover;
  z-index: 1;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(18, 230, 219, 0.1) 0%, rgba(18, 230, 219, 0.05) 50%, rgba(18, 230, 219, 0.1) 100%);
    border: 1px solid rgba(18, 230, 219, 0.3);
    border-radius: 8px;
  }
}

.card-content {
  position: relative;
  height: 100%;
  padding: 16px 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 2;
}

.card-title {
  color: #ffffff;
  font-size: 14px;
  font-weight: 400;
  opacity: 0.8;
  margin-bottom: 8px;
}

.card-value {
  display: flex;
  align-items: baseline;
  gap: 4px;

  :deep(.count-to) {
    color: #2bccff;
    font-size: 24px;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(43, 204, 255, 0.5);
  }

  .card-unit {
    color: #ffffff;
    font-size: 14px;
    font-weight: 400;
    opacity: 0.8;
  }
}

.card-icon {
  position: absolute;
  top: 16px;
  right: 20px;
  width: 32px;
  height: 32px;

  .icon-bg {
    width: 100%;
    height: 100%;
    background: rgba(43, 204, 255, 0.2);
    border-radius: 50%;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 16px;
      height: 16px;
      background: #2bccff;
      border-radius: 2px;
      box-shadow: 0 0 8px rgba(43, 204, 255, 0.6);
    }
  }
}
