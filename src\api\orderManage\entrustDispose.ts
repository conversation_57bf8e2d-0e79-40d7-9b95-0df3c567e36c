import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

/**
 * 委托处置相关API接口
 */
enum Api {
  // 查询列表
  queryPageAll = '/hgy/entrustService/hgyAssetEntrust/queryPageAll',
  // 根据ID查询详情
  queryEntrustById = '/hgy/entrustService/hgyAssetEntrust/queryEntrustById',
  // 新增
  add = '/hgy/entrustService/hgyAssetEntrust/add',
  // 编辑
  edit = '/hgy/entrustService/hgyAssetEntrust/edit',
  // 删除单个
  deleteOne = '/hgy/entrustService/hgyAssetEntrust/delete',
  // 自定义删除（委托处置和自主处置通用）
  customEntrustDelete = '/hgy/entrustService/hgyAssetEntrust/customEntrustDelete',
  // 批量删除
  deleteBatch = '/hgy/entrustService/hgyAssetEntrust/deleteBatch',
  // 撤拍
  withdraw = '/hgy/entrustService/hgyAssetEntrust/withdraw',
  // 导出
  exportXls = '/hgy/entrustService/hgyAssetEntrust/exportXls',
  // 导入
  importExcel = '/hgy/entrustService/hgyAssetEntrust/importExcel',
}

/**
 * 委托处置记录接口
 */
export interface EntrustDisposeRecord {
  hgyAssetEntrust: hgyAssetEntrust;
  hgyEntrustOrder: hgyEntrustOrder;
  hgyAttachmentList: hgyAttachmentList[];
}

interface hgyAssetEntrust {
  /* 资产处置ID */
  id: number;
  /* 租户ID */
  tenantId: number;
  /* 用户ID */
  userId: number;
  /* 委托单ID */
  entrustOrderId: number;
  /* 资产名称 */
  assetName: string;
  /* 资产编号 */
  assetNo: string;
  /* 资产类型 */
  assetType: number;
  /* 资产类型第一级 */
  assetTypeOne?: string;
  /* 资产类型第二级 */
  assetTypeTwo?: string;
  /* 资产类型第三级 */
  assetTypeThree?: string;
  /* 资产数量 */
  quantity: number;
  /* 是否展示实际数量 */
  quantityFlag: string;
  /* 实际数量 */
  actualQuantity: string;
  /* 计量单位 */
  unit: string;
  /* 使用年限 */
  serviceLife: number;
  /* 折旧程度 */
  depreciationDegree: number;
  /* 当前状态 */
  currentStatus: number;
  /* 评估价值 */
  appraisalValue: number;
  /* 处置底价 */
  disposalPrice: number;
  /* 处置开始时间 */
  disposalStartTime: string;
  /* 处置结束时间 */
  disposalEndTime: string;
  /* 省份 */
  province: string;
  /* 城市 */
  city: string;
  /* 区县 */
  district: string;
  /* 详细地址 */
  address: string;
  /* 付款方式 */
  paymentMethod: number;
  /* 是否含税 */
  isTaxIncluded: number;
  /* 特殊说明 */
  specialNotes: string;
  /* 创建时间 */
  createTime: string;
  /* 更新时间 */
  updateTime: string;
  /* 删除状态 */
  delFlag: number;
  /* 创建人 */
  createBy: string;
  /* 更新人 */
  updateBy: string;
  /* 交易状态 */
  transactionStatus: string;
  /* 审核状态 */
  status: number;
  /* 省份编码 */
  provinceCode: string;
  /* 城市编码 */
  cityCode: string;
  /* 区县编码 */
  districtCode: string;
  /* 服务费支付方式 */
  servicePayType: number;
  /* 交易价格 */
  transactionPrice: number;
  /* 溢价额 */
  premiumAmount: number;
  /* 溢价率 */
  premiumRate: number;
  /* 封面图片路径 */
  coverImage: string;
  /* 拍卖方式 */
  auctionModel: number;
}

interface hgyEntrustOrder {
  /* 委托单ID */
  id: string;
  /* 租户ID */
  tenantId: number;
  /* 用户ID */
  userId: string;
  /* 委托企业ID */
  entrustCompanyId: number;
  /* 委托企业名称 */
  entrustCompanyName: string;
  /* 受委托企业ID */
  onEntrustCompanyId: number;
  /* 受委托企业名称 */
  onEntrustCompanyName: string;
  /* 委托类型 */
  entrustType: number;
  /* 服务类型 */
  serviceType: number;
  /* 状态 */
  status: number;
  /* 联系人 */
  relationUser: string;
  /* 联系电话 */
  relationPhone: string;
  /* 审核意见 */
  auditOpinion: string;
  /* 审核时间 */
  auditTime: string;
  /* 审核人 */
  auditUser: string;
  /* 创建时间 */
  createTime: string;
  /* 更新时间 */
  updateTime: string;
  /* 删除状态 */
  delFlag: number;
  /* 创建人 */
  createBy: string;
  /* 更新人 */
  updateBy: string;
}

interface hgyAttachmentList {
  /* 附件ID */
  id: string;
  /* 租户ID */
  tenantId: number;
  /* 用户ID */
  userId: string;
  /* 业务类型 */
  bizType: string;
  /* 业务ID */
  bizId: string;
  /* 文件名称 */
  fileName: string;
  /* 文件路径 */
  filePath: string;
  /* 文件大小(字节) */
  fileSize: number;
  /* 文件类型 */
  fileType: string;
  /* 上传时间 */
  createTime: string;
  /* 删除状态 */
  delFlag: number;
  /* 创建人 */
  createBy: string;
  /* 更新人 */
  updateBy: string;
  /* 更新时间 */
  updateTime: string;
}

/**
 * 分页查询参数
 */
export interface QueryPageParams {
  pageNo?: number;
  pageSize?: number;
  /* entrustOrderId?: string;
  assetName?: string;
  entrustCompanyName?: string;
  status?: string;
  [key: string]: any; */
  itemName?: string; // 标的名称
  status?: number; // 状态
  auctionDateEnd?: string; // 拍卖结束时间
  entrustStatus?: string; // 拍卖开始时间
}

/**
 * 分页查询结果
 */
export interface PageResult<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * 查询委托处置列表
 * @param params 查询参数
 * @returns Promise<PageResult<EntrustDisposeRecord>> 返回分页的委托处置列表
 */
export const queryPageAll = (params?: QueryPageParams) => {
  return defHttp.post<PageResult<EntrustDisposeRecord>>({
    url: Api.queryPageAll,
    params,
  });
};

/**
 * 根据ID查询委托详情
 * @param id 委托ID
 * @returns Promise<EntrustDisposeRecord> 返回委托详情
 */
export const queryEntrustById = (id: string) => {
  return defHttp.get<EntrustDisposeRecord>({
    url: Api.queryEntrustById,
    params: { id },
  });
};

/**
 * 新增委托处置
 * @param params 委托处置信息
 * @returns Promise<any> 返回新增结果
 */
export const addEntrustDispose = (params: Partial<EntrustDisposeRecord>) => {
  return defHttp.post({
    url: Api.add,
    params,
  });
};

/**
 * 编辑委托处置
 * @param params 委托处置信息
 * @returns Promise<any> 返回编辑结果
 */
export const editEntrustDispose = (params: Partial<EntrustDisposeRecord> & { id: string }) => {
  return defHttp.put({
    url: Api.edit,
    params,
  });
};

/**
 * 删除单个委托处置
 * @param params 删除参数
 * @param handleSuccess 成功回调
 */
export const deleteOne = (params: { id: string }, handleSuccess: () => void) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 自定义删除委托处置（委托处置和自主处置通用）
 * @param id 删除项的ID
 * @returns Promise<any> 返回删除结果
 */
export const customEntrustDelete = (id: string) => {
  return defHttp.delete({
    url: `${Api.customEntrustDelete}?id=${id}`,
  });
};

/**
 * 批量删除委托处置
 * @param params 删除参数
 * @param handleSuccess 成功回调
 */
export const batchDelete = (params: { ids: string[] }, handleSuccess: () => void) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 撤拍委托处置
 * @param params 撤拍参数
 * @returns Promise<any> 返回撤拍结果
 */
export const withdrawEntrustDispose = (params: { id: string }) => {
  return defHttp.post({
    url: Api.withdraw,
    params,
  });
};

/**
 * 导出Excel
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入Excel
 */
export const getImportUrl = Api.importExcel;
