<template>
  <div class="supply-demand-audit">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <a-spin size="large" tip="加载中..." />
    </div>

    <!-- 审核内容 -->
    <div v-else class="audit-container" :class="{ 'view-only-mode': props.viewOnly || isAudited }">
      <!-- 基本信息展示 -->
      <div class="info-section">
        <span class="section-title">基本信息</span>
        <div class="info-grid-four">
          <div class="info-item">
            <span class="label">供求单号：</span>
            <span class="value">{{ auditData?.hgySupplyDemand?.id || auditData?.id || '-' }}</span>
          </div>

          <!-- 供应需求基本信息 -->
          <div class="info-item">
            <span class="label">供应标题：</span>
            <span class="value">{{ auditData?.hgySupplyDemand?.infoTitle || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">物资类型：</span>
            <span class="value">{{
              materialTypeName || auditData?.hgySupplyDemand?.materialType_dictText || auditData?.hgySupplyDemand?.materialType || '-'
            }}</span>
          </div>
          <div class="info-item">
            <span class="label">联系人：</span>
            <span class="value">{{ auditData?.hgySupplyDemand?.relationUser || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">联系电话：</span>
            <span class="value">{{ auditData?.hgySupplyDemand?.relationPhone || '-' }}</span>
          </div>

          <!-- 通用信息 -->
          <div class="info-item">
            <span class="label">创建时间：</span>
            <span class="value">{{ formatDateTime(auditData?.hgySupplyDemand?.createTime) }}</span>
          </div>
        </div>
      </div>

      <!-- 供应需求详情 -->
      <div class="detail-section">
        <span class="section-title">供应需求详情</span>
        <div class="detail-content scrollable-content" :class="{ 'view-only': props.viewOnly }">
          <div class="detail-grid">
            <!-- 供应需求信息 -->
            <div v-if="auditData?.hgySupplyDemand" class="detail-group">
              <div class="info-grid">
                <div class="info-item">
                  <span class="label">供应标题：</span>
                  <span class="value">{{ auditData.hgySupplyDemand.infoTitle || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">物资类型：</span>
                  <span class="value">{{
                    materialTypeName || auditData.hgySupplyDemand.materialType_dictText || auditData.hgySupplyDemand.materialType || '-'
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="label">物资品牌：</span>
                  <span class="value">{{ auditData.hgySupplyDemand.brand || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">物资型号：</span>
                  <span class="value">{{ auditData.hgySupplyDemand.model || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">新旧程度：</span>
                  <span class="value">{{
                    auditData.hgySupplyDemand.depreciationDegree_dictText || getDepreciationText(auditData.hgySupplyDemand.depreciationDegree)
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="label">物资数量：</span>
                  <span class="value">{{ auditData.hgySupplyDemand.quantity || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">物资单位：</span>
                  <span class="value">{{ auditData.hgySupplyDemand.unit || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">物资价格：</span>
                  <span class="value">¥{{ formatPrice(auditData.hgySupplyDemand.price) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">存放方式：</span>
                  <span class="value">{{
                    auditData.hgySupplyDemand.storageMethod_dictText || getStorageMethodText(auditData.hgySupplyDemand.storageMethod)
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="label">所在地区：</span>
                  <span class="value">{{ getFullAddress(auditData.hgySupplyDemand) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">有效期：</span>
                  <span class="value">{{ formatDateTime(auditData.hgySupplyDemand.validDate) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">联系人：</span>
                  <span class="value">{{ auditData.hgySupplyDemand.relationUser || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">联系电话：</span>
                  <span class="value">{{ auditData.hgySupplyDemand.relationPhone || '-' }}</span>
                </div>
              </div>

              <!-- 供应亮点 -->
              <div v-if="auditData.hgySupplyDemand.highlights" class="info-item">
                <span class="label">供应亮点：</span>
                <span class="value">{{ auditData.hgySupplyDemand.highlights }}</span>
              </div>

              <!-- 物资详细描述 -->
              <div v-if="auditData.hgySupplyDemand.materialDesc" class="info-item full-width">
                <span class="label">物资详细描述：</span>
                <div class="rich-content" v-html="auditData.hgySupplyDemand.materialDesc"></div>
              </div>
            </div>

            <!-- 附件信息 -->
            <div v-if="auditData?.attachmentList && auditData.attachmentList.length > 0" class="detail-group">
              <h4 class="group-title">附件信息</h4>
              <div class="attachment-list">
                <div v-for="attachment in auditData.attachmentList" :key="attachment.id" class="attachment-item">
                  <div class="attachment-info">
                    <span class="file-name">{{ attachment.fileName }}</span>
                    <span class="file-size">{{ formatFileSize(attachment.fileSize) }}</span>
                    <a-button type="link" size="small" @click="previewFile(attachment)">预览</a-button>
                    <a-button type="link" size="small" @click="downloadAttachment(attachment)">下载</a-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 审核详情区域 -->
      <div v-if="isAudited" class="audit-details">
        <span class="section-title">审核详情</span>
        <div class="audit-info">
          <div class="audit-result">
            <span class="label">审核结果：</span>
            <a-tag :color="auditData?.hgySupplyDemand?.status === 3 ? 'green' : 'red'">
              {{ auditData?.hgySupplyDemand?.status === 3 ? '通过' : '拒绝' }}
            </a-tag>
          </div>
          <div v-if="auditData?.hgySupplyDemand?.auditOpinion" class="audit-opinion">
            <span class="label">审核意见：</span>
            <span class="content">{{ auditData.hgySupplyDemand.auditOpinion }}</span>
          </div>
        </div>
      </div>

      <!-- 审核操作区域 -->
      <div v-if="!props.viewOnly && !isAudited" class="audit-actions">
        <span class="section-title">审核操作</span>
        <div class="audit-form">
          <a-form ref="auditFormRef" :model="auditForm" :rules="auditRules" layout="vertical">
            <a-form-item label="审核结果" name="result" required>
              <a-radio-group v-model:value="auditForm.result">
                <a-radio :value="3">通过</a-radio>
                <a-radio :value="4">拒绝</a-radio>
              </a-radio-group>
            </a-form-item>

            <a-form-item label="审核意见" name="remark">
              <a-textarea v-model:value="auditForm.remark" placeholder="请输入审核意见" :rows="4" :max-length="500" show-count />
            </a-form-item>
          </a-form>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" :loading="submitting" @click="handleSubmit"> 确认审核 </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue';
  import { message } from 'ant-design-vue';
  import type { FormInstance } from 'ant-design-vue';
  import type { AuditRecord } from '../types';
  import { getAuditDetailByType, reviewByServiceType } from '/@/api/manageCenter/audit';
  import { queryMaterialTypeById } from '/@/api/supplyAndDemand/SupplyDemand';
  import { useFilePreview } from '/@/components/FilePreview/src/useFilePreview';

  // Props 定义
  interface Props {
    record: AuditRecord;
    serviceType: number; // 4-供应 5-求购
    viewOnly?: boolean; // 是否只查看，不显示审核操作
  }

  // Emits 定义
  interface Emits {
    (e: 'close'): void;
    (e: 'auditSuccess'): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    viewOnly: false,
  });

  const emit = defineEmits<Emits>();

  // 响应式数据
  const loading = ref(false);
  const submitting = ref(false);
  const auditData = ref<any>(null);
  const materialTypeName = ref<string>('');

  // 文件预览
  const { openPreview, isSupportPreview } = useFilePreview();

  // 审核表单
  const auditFormRef = ref<FormInstance>();
  const auditForm = reactive({
    result: undefined as number | undefined,
    remark: '',
  });

  // 审核表单验证规则
  const auditRules = {
    result: [{ required: true, message: '请选择审核结果', trigger: 'change' }],
    remark: [{ required: true, message: '请输入审核意见', trigger: 'change' }],
  };

  // 计算属性：是否已审核
  const isAudited = computed(() => {
    return auditData.value?.hgySupplyDemand?.status === 3 || auditData.value?.hgySupplyDemand?.status === 4;
  });

  // 获取审核详情数据
  async function fetchAuditData() {
    console.log('fetchAuditData - props.record:', props.record);
    console.log('fetchAuditData - props.serviceType:', props.serviceType);

    if (!props.record?.id) {
      console.error('fetchAuditData - 缺少record或record.id');
      return;
    }

    loading.value = true;
    try {
      // 根据委托类型和服务类型调用不同的API获取详细数据
      const response = await getAuditDetailByType(
        props.record.id,
        props.record.entrustType || 3, // 供求类型
        props.serviceType || 4 // 默认供应
      );
      console.log('获取到的供求审核详情:', response);
      // 适配数据结构：如果返回的是直接的供应需求数据，包装成期望的结构
      if (response && !response.hgySupplyDemand) {
        auditData.value = {
          hgySupplyDemand: response,
          attachmentList: response.attachmentList || [],
        };
      } else {
        auditData.value = response;
      }

      // 获取物资类型名称
      await fetchMaterialTypeName();
    } catch (error) {
      console.error('获取审核详情失败:', error);
      message.error('获取审核详情失败');
      // 如果API调用失败，构造一个符合模板期望的数据结构
      auditData.value = {
        hgySupplyDemand: {
          id: props.record.id,
          infoTitle: props.record.projectName,
          status: props.record.status,
          relationUser: props.record.relationUser,
          relationPhone: props.record.relationPhone,
          createTime: props.record.submitTime,
          auditTime: props.record.auditTime,
        },
        attachmentList: [],
      };
    } finally {
      loading.value = false;
    }
  }

  // 获取物资类型名称
  async function fetchMaterialTypeName() {
    try {
      const materialTypeId = auditData.value?.hgySupplyDemand?.materialType;
      if (!materialTypeId) {
        materialTypeName.value = '';
        return;
      }

      const response = await queryMaterialTypeById({ id: materialTypeId });
      console.log('获取到的物资类型信息:', response);

      // 根据实际返回的数据结构调整
      materialTypeName.value = response?.hgyMaterialType?.name || response?.name || '';
    } catch (error) {
      console.error('获取物资类型名称失败:', error);
      materialTypeName.value = '';
    }
  }

  // 提交审核
  async function handleSubmit() {
    try {
      await auditFormRef.value?.validate();
    } catch (error) {
      return;
    }

    submitting.value = true;

    try {
      // 根据服务类型调用对应的审核API
      await reviewByServiceType(props.serviceType, {
        id: props.record.id,
        status: auditForm.result!,
        auditOpinion: auditForm.remark,
      });
      emit('auditSuccess');
      emit('close');
    } catch (error) {
      console.error('审核提交失败:', error);
      message.error('审核提交失败');
    } finally {
      submitting.value = false;
    }
  }

  // 取消审核
  function handleCancel() {
    emit('close');
  }

  // 格式化日期时间
  function formatDateTime(dateTime: string | null | undefined) {
    if (!dateTime) return '-';
    try {
      const date = new Date(dateTime);
      if (isNaN(date.getTime())) return '-';
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      });
    } catch (error) {
      return '-';
    }
  }

  // 格式化价格
  function formatPrice(price: number) {
    if (!price && price !== 0) return '-';
    return price.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
  }

  // 获取新旧程度文本
  function getDepreciationText(degree: number) {
    if (!degree) return '-';
    const degreeMap = {
      1: '一成新',
      2: '二成新',
      3: '三成新',
      4: '四成新',
      5: '五成新',
      6: '六成新',
      7: '七成新',
      8: '八成新',
      9: '九成新',
    };
    return degreeMap[degree] || '未知';
  }

  // 获取存放方式文本
  function getStorageMethodText(method: number) {
    if (!method) return '-';
    const methodMap = {
      1: '仓库',
      2: '露天堆放',
      3: '集装箱',
      4: '其他',
    };
    return methodMap[method] || '未知';
  }

  // 获取完整地址
  function getFullAddress(data: any) {
    if (!data) return '-';

    // 优先使用字典文本字段
    const province = data.province_dictText || data.provinceName || '';
    const city = data.city_dictText || data.cityName || '';
    const district = data.district_dictText || data.districtName || '';
    const address = data.address || '';

    const parts = [province, city, district, address].filter(Boolean);
    return parts.length > 0 ? parts.join(' ') : '-';
  }

  // 格式化文件大小
  function formatFileSize(size: number) {
    if (!size) return '-';
    if (size < 1024) return size + 'B';
    if (size < 1024 * 1024) return (size / 1024).toFixed(1) + 'KB';
    if (size < 1024 * 1024 * 1024) return (size / (1024 * 1024)).toFixed(1) + 'MB';
    return (size / (1024 * 1024 * 1024)).toFixed(1) + 'GB';
  }

  // 预览文件
  function previewFile(attachment: any) {
    viewAttachment(attachment);
  }

  // 查看附件
  function viewAttachment(attachment: any) {
    if (!attachment.filePath) {
      message.warning('附件路径不存在');
      return;
    }

    // 检查是否支持预览
    if (isSupportPreview(attachment.fileName)) {
      openPreview({
        fileName: attachment.fileName,
        filePath: attachment.filePath,
        fileType: attachment.fileType,
      });
    } else {
      // 不支持预览的文件直接下载
      downloadAttachment(attachment);
    }
  }

  // 下载附件
  function downloadAttachment(attachment: any) {
    if (!attachment.filePath) {
      message.warning('附件路径不存在');
      return;
    }

    // 创建下载链接
    const link = document.createElement('a');
    link.href = attachment.filePath;
    link.download = attachment.fileName || '附件';
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  onMounted(() => {
    fetchAuditData();
  });
</script>

<style lang="less" scoped>
  .supply-demand-audit {
    padding: 0;

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 60px 0;
      color: #666;
    }

    .audit-container {
      .info-section,
      .detail-section,
      .audit-details,
      .audit-actions {
        .section-title {
          display: flex;
          align-items: center;
          font-size: 16px;
          color: #262626;
          margin-bottom: 10px;
          padding-bottom: 8px;
          font-family: 'PingFang Bold', sans-serif;
          &::before {
            content: '';
            display: block;
            width: 4px;
            height: 20px;
            background-color: #004c66;
            margin-right: 10px;
          }
        }
      }

      .info-grid-four {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 10px;
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 6px;
        border: 1px solid #e9ecef;

        @media (max-width: 1200px) {
          grid-template-columns: repeat(3, 1fr);
        }

        @media (max-width: 768px) {
          grid-template-columns: repeat(2, 1fr);
        }

        @media (max-width: 480px) {
          grid-template-columns: 1fr;
        }
      }

      .info-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;

        @media (max-width: 768px) {
          grid-template-columns: repeat(2, 1fr);
        }

        @media (max-width: 480px) {
          grid-template-columns: 1fr;
        }
      }

      .info-item {
        display: flex;
        align-items: flex-start;
        min-height: 32px;

        &.full-width {
          grid-column: 1 / -1;
          flex-direction: column;
          align-items: stretch;

          .label {
            margin-bottom: 8px;
          }

          .value,
          .rich-content {
            flex: 1;
            line-height: 1.6;
          }

          .rich-content {
            background-color: #fff;
            border-radius: 6px;
            border: 1px solid #e8e8e8;
            padding: 10px;
            min-height: 100px;

            :deep(p) {
              margin-bottom: 8px;
              &:last-child {
                margin-bottom: 0;
              }
            }

            :deep(img) {
              max-width: 100%;
              height: auto;
            }
          }
        }

        .label {
          font-weight: 500;
          color: #595959;
          white-space: nowrap;
          margin-right: 12px;
          min-width: 80px;
        }

        .value {
          flex: 1;
          color: #262626;
          word-break: break-all;
        }
      }

      .detail-content {
        .detail-grid {
          display: flex;
          flex-direction: column;
          gap: 24px;
        }

        .detail-group {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 6px;
          border: 1px solid #e9ecef;
          .group-title {
            font-size: 14px;
            font-weight: 600;
            color: #1890ff;
            margin-bottom: 16px;
            padding-left: 8px;
            border-left: 3px solid #1890ff;
          }
        }
      }

      // 查看模式下增加详情区域高度
      &.view-only-mode {
        .detail-section {
          .detail-content {
            min-height: 400px; // 增加最小高度

            .detail-grid {
              gap: 32px; // 增加间距
            }

            .detail-group {
              padding: 24px; // 增加内边距

              .info-grid,
              .info-grid-four {
                padding: 24px; // 增加网格内边距
                gap: 16px; // 增加网格间距
              }

              .info-item {
                min-height: 40px; // 增加项目最小高度

                &.full-width {
                  .rich-content {
                    min-height: 120px; // 增加富文本内容高度
                    padding: 16px; // 增加富文本内边距
                  }
                }
              }
            }
          }
        }

        .attachment-section {
          .attachment-list {
            gap: 16px; // 增加附件列表间距

            .attachment-item {
              padding: 16px; // 增加附件项内边距
            }
          }
        }
      }

      .scrollable-content {
        max-height: 280px;
        min-height: 200px;
        overflow-y: auto;
        padding-right: 8px;

        &.view-only {
          min-height: 320px;
        }

        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;

          &:hover {
            background: #a8a8a8;
          }
        }
      }

      .attachment-list {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 12px;

        .attachment-item {
          padding: 12px;
          background-color: #fafafa;
          border-radius: 6px;
          border: 1px solid #d9d9d9;

          .attachment-info {
            display: flex;
            align-items: center;
            gap: 8px;

            .file-name {
              flex: 1;
              font-weight: 500;
              color: #262626;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .file-size {
              color: #8c8c8c;
              font-size: 12px;
              white-space: nowrap;
            }
          }
        }
      }

      .audit-details {
        .audit-info {
          padding: 16px;
          background-color: #f6f8fa;
          border-radius: 6px;
          border: 1px solid #e1e4e8;

          .audit-result {
            display: flex;
            align-items: center;
            margin-bottom: 12px;

            .label {
              font-weight: 500;
              color: #595959;
              margin-right: 12px;
            }
          }

          .audit-opinion {
            .label {
              font-weight: 500;
              color: #595959;
              margin-right: 12px;
            }

            .content {
              color: #262626;
              line-height: 1.6;
            }
          }
        }
      }

      .audit-actions {
        .audit-form {
          margin-bottom: 24px;
        }

        .action-buttons {
          display: flex;
          justify-content: center;
          gap: 12px;
        }
      }
    }
  }
</style>
