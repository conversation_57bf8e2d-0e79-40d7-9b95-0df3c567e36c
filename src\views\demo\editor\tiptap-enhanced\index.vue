<template>
  <PageWrapper title="增强版Tiptap富文本编辑器示例">
    <div class="p-4">
      <a-card title="段落格式和模板功能测试">
        <div class="mb-4">
          <a-button-group>
            <a-button @click="getValue">获取内容</a-button>
            <a-button @click="setValue">设置内容</a-button>
            <a-button @click="clearValue">清空内容</a-button>
            <a-button @click="toggleDisabled">{{ disabled ? '启用' : '禁用' }}</a-button>
            <a-button @click="toggleTemplates">{{ showTemplates ? '隐藏模板' : '显示模板' }}</a-button>
          </a-button-group>
        </div>

        <!-- 使用 JEditorTiptap 组件 -->
        <JEditorTiptap
          v-model:value="content"
          :disabled="disabled"
          :templates="showTemplates ? templates : []"
          height="400px"
          placeholder="请输入内容，体验增强版富文本编辑器..."
          @change="handleChange"
        />

        <div class="mt-4">
          <h4>实时内容预览：</h4>
          <div class="content-preview" v-html="content"></div>
        </div>

        <div class="mt-4">
          <h4>功能说明：</h4>
          <ul>
            <li
              ><strong>段落格式：</strong>支持首行缩进、行间距、段落间距设置
              <ul>
                <li>选中段落后，点击工具栏中的"无缩进"、"单倍行距"、"无间距"按钮</li>
                <li>从下拉菜单中选择相应的格式选项</li>
                <li>格式会立即应用到选中的段落</li>
              </ul>
            </li>
            <li><strong>模板功能：</strong>当编辑器为空且获得焦点时，自动提示使用模板</li>
            <li><strong>手动使用模板：</strong>点击工具栏中的模板按钮可手动选择模板</li>
            <li><strong>API兼容性：</strong>保持与原 JEditor 组件完全兼容</li>
          </ul>
        </div>
      </a-card>

      <a-card title="新功能测试：templateType 参数" class="mt-4">
        <div class="mb-4">
          <a-space>
            <span>选择模板类型：</span>
            <a-select v-model:value="selectedTemplateType" style="width: 200px" placeholder="选择模板类型">
              <a-select-option value="auction-notice">拍卖公告</a-select-option>
              <a-select-option value="auction-rules">拍卖须知</a-select-option>
              <a-select-option value="important-notice">重要声明</a-select-option>
              <a-select-option value="item-introduction">标的介绍</a-select-option>
              <a-select-option value="material-description">物资详细描述</a-select-option>
            </a-select>
            <a-button @click="clearTemplateContent">清空内容</a-button>
          </a-space>
        </div>

        <!-- 使用 templateType 参数 -->
        <JEditorTiptap
          v-model:value="templateContent"
          :template-type="selectedTemplateType"
          height="400px"
          placeholder="选择模板类型后，编辑器为空时获得焦点会自动提示使用模板..."
          @change="handleTemplateChange"
        />

        <div class="mt-4">
          <h4>功能说明：</h4>
          <ul>
            <li><strong>单一模板类型：</strong>通过 templateType 参数指定特定的模板类型</li>
            <li><strong>自动提示：</strong>编辑器为空且获得焦点时自动提示使用模板</li>
            <li><strong>手动使用：</strong>点击工具栏模板按钮会确认是否清空当前内容</li>
            <li><strong>全屏编辑：</strong>点击全屏按钮或按ESC键切换全屏模式</li>
          </ul>
        </div>
      </a-card>

      <a-card title="直接使用 TiptapEditor 组件" class="mt-4">
        <div class="mb-4">
          <a-button-group>
            <a-button @click="getDirectValue">获取内容</a-button>
            <a-button @click="setDirectValue">设置内容</a-button>
            <a-button @click="clearDirectValue">清空内容</a-button>
          </a-button-group>
        </div>

        <!-- 直接使用 TiptapEditor 组件 -->
        <TiptapEditor
          v-model="directContent"
          :templates="templates"
          height="300px"
          placeholder="直接使用 TiptapEditor 组件..."
          @change="handleDirectChange"
        />

        <div class="mt-4">
          <h4>实时内容预览：</h4>
          <div class="content-preview" v-html="directContent"></div>
        </div>
      </a-card>
    </div>
  </PageWrapper>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { PageWrapper } from '/@/components/Page';
  import { TiptapEditor } from '/@/components/TiptapEditor';
  import JEditorTiptap from '/@/components/Form/src/jeecg/components/JEditorTiptap.vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getAllTemplates, TemplateType } from '/@/utils/template';

  const { createMessage } = useMessage();

  // JEditorTiptap 相关状态
  const content = ref(`
    <p>这是第一段内容，可以测试段落格式功能。</p>
    <p>这是第二段内容，您可以选中文本后使用工具栏中的段落格式功能：</p>
    <ul>
      <li>缩进：设置段落首行缩进</li>
      <li>行距：调整段落内行与行之间的距离</li>
      <li>间距：设置段落与段落之间的间距</li>
    </ul>
    <p>请尝试选中不同的段落，然后使用工具栏中的段落格式功能进行测试。</p>
  `);
  const disabled = ref(false);
  const showTemplates = ref(true);

  // TiptapEditor 相关状态
  const directContent = ref('');

  // 模板类型测试相关状态
  const selectedTemplateType = ref<TemplateType>('auction-notice');
  const templateContent = ref('');

  // 模板数据
  const templates = getAllTemplates();

  // JEditorTiptap 事件处理
  const handleChange = (value: string) => {
    console.log('JEditorTiptap 内容变化:', value);
  };

  const getValue = () => {
    createMessage.info(`当前内容长度: ${content.value.length} 字符`);
    console.log('当前内容:', content.value);
  };

  const setValue = () => {
    content.value = `
      <h2 style="text-align: center;">测试标题</h2>
      <p style="text-indent: 2em; line-height: 1.8;">这是一个带有首行缩进和行间距的段落。可以测试段落格式功能是否正常工作。</p>
      <p style="margin-top: 16px; margin-bottom: 16px;">这是一个带有段落间距的段落。</p>
      <ul>
        <li>列表项 1</li>
        <li>列表项 2</li>
        <li>列表项 3</li>
      </ul>
    `;
    createMessage.success('已设置测试内容');
  };

  const clearValue = () => {
    content.value = '';
    createMessage.success('已清空内容');
  };

  const toggleDisabled = () => {
    disabled.value = !disabled.value;
    createMessage.info(disabled.value ? '已禁用编辑器' : '已启用编辑器');
  };

  const toggleTemplates = () => {
    showTemplates.value = !showTemplates.value;
    createMessage.info(showTemplates.value ? '已显示模板功能' : '已隐藏模板功能');
  };

  // TiptapEditor 事件处理
  const handleDirectChange = (value: string) => {
    console.log('TiptapEditor 内容变化:', value);
  };

  const getDirectValue = () => {
    createMessage.info(`当前内容长度: ${directContent.value.length} 字符`);
    console.log('当前内容:', directContent.value);
  };

  const setDirectValue = () => {
    directContent.value = `
      <h1>直接使用 TiptapEditor</h1>
      <p style="text-indent: 2em; line-height: 2;">这是通过 TiptapEditor 组件直接设置的内容，包含段落格式。</p>
    `;
    createMessage.success('已设置测试内容');
  };

  const clearDirectValue = () => {
    directContent.value = '';
    createMessage.success('已清空内容');
  };

  // 模板类型测试相关方法
  const handleTemplateChange = (value: string) => {
    console.log('模板内容变化:', value);
  };

  const clearTemplateContent = () => {
    templateContent.value = '';
    createMessage.success('已清空模板内容');
  };
</script>

<style lang="less" scoped>
  .content-preview {
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    padding: 16px;
    background: #fafafa;
    min-height: 100px;
    max-height: 300px;
    overflow-y: auto;

    :deep(h1),
    :deep(h2),
    :deep(h3),
    :deep(h4),
    :deep(h5),
    :deep(h6) {
      margin: 16px 0 8px 0;
      font-weight: 600;

      &:first-child {
        margin-top: 0;
      }
    }

    :deep(p) {
      margin: 8px 0;
      line-height: 1.6;

      &:last-child {
        margin-bottom: 0;
      }
    }

    :deep(ul),
    :deep(ol) {
      margin: 8px 0;
      padding-left: 24px;

      li {
        margin: 4px 0;
      }
    }

    :deep(table) {
      width: 100%;
      border-collapse: collapse;
      margin: 16px 0;

      th,
      td {
        padding: 8px 12px;
        border: 1px solid #e8e8e8;
        text-align: left;
      }

      th {
        background: #f5f5f5;
        font-weight: 600;
      }
    }
  }
</style>
