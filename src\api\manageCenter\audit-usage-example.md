# 审核接口使用示例

## 概述

本文档展示如何使用新的统一审核接口，这些接口支持资产处置、采购信息和竞价委托的审核操作。已删除旧的`submitSelfAudit`和`submitAppreciationAudit`接口，统一使用新的审核接口。

## 接口列表

### 1. 资产处置审核接口

- **接口地址**: `POST /hgy/entrustService/hgyAssetEntrust/review`
- **说明**: 不区分增值委托和自主委托

### 2. 采购审核接口

- **接口地址**: `POST /hgy/entrustService/hgyProcurement/review`
- **说明**: 不区分增值委托和自主委托

### 3. 竞价审核接口

- **接口地址**: `PUT /hgy/auction/hgyAuctionItemTemp/reviewOrderAndItemTemp`
- **说明**: 支持增值委托和自主委托的竞价审核

## 参数说明

所有审核接口都使用相同的参数结构：

```typescript
interface ReviewParams {
  id: string; // 资产/采购/竞价的ID
  status: number; // 3-通过 4-拒绝
  auditOpinion?: string; // 审核意见
}
```

## 使用示例

### 1. 直接调用特定审核接口

```typescript
import { reviewAssetEntrust, reviewProcurement, reviewAuctionItemTemp } from '@/api/manageCenter/audit';

// 资产处置审核
const reviewAsset = async () => {
  try {
    const result = await reviewAssetEntrust({
      id: 'asset123',
      status: 3, // 通过
      auditOpinion: '审核通过，资产信息完整',
    });
    console.log('资产处置审核成功:', result);
  } catch (error) {
    console.error('资产处置审核失败:', error);
  }
};

// 采购审核
const reviewPurchase = async () => {
  try {
    const result = await reviewProcurement({
      id: 'procurement456',
      status: 4, // 拒绝
      auditOpinion: '采购信息不完整，请补充相关资料',
    });
    console.log('采购审核成功:', result);
  } catch (error) {
    console.error('采购审核失败:', error);
  }
};

// 竞价审核
const reviewAuction = async () => {
  try {
    const result = await reviewAuctionItemTemp({
      id: 'auction789',
      status: 3, // 通过
      auditOpinion: '竞价标的信息符合要求',
    });
    console.log('竞价审核成功:', result);
  } catch (error) {
    console.error('竞价审核失败:', error);
  }
};
```

### 2. 使用统一审核接口

```typescript
import { reviewByServiceType } from '@/api/manageCenter/audit';

// 根据服务类型统一审核
const handleReview = async (serviceType: number, id: string, status: number, auditOpinion?: string) => {
  try {
    const result = await reviewByServiceType(serviceType, {
      id,
      status,
      auditOpinion,
    });

    const serviceNames = {
      1: '竞价委托',
      2: '资产处置',
      3: '采购信息',
    };

    console.log(`${serviceNames[serviceType]}审核成功:`, result);
    return result;
  } catch (error) {
    console.error('审核失败:', error);
    throw error;
  }
};

// 使用示例
handleReview(2, 'asset123', 3, '审核通过'); // 资产处置审核通过
handleReview(3, 'procurement456', 4, '信息不完整'); // 采购审核拒绝
handleReview(1, 'auction789', 3, '符合要求'); // 竞价审核通过
```

### 3. 在Vue组件中使用

```vue
<template>
  <div>
    <a-button @click="handleApprove" type="primary">审核通过</a-button>
    <a-button @click="handleReject" danger>审核拒绝</a-button>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { message } from 'ant-design-vue';
  import { reviewByServiceType } from '@/api/manageCenter/audit';

  const props = defineProps<{
    id: string;
    serviceType: number; // 1-竞价 2-资产处置 3-采购
  }>();

  const auditOpinion = ref('');

  // 审核通过
  const handleApprove = async () => {
    try {
      await reviewByServiceType(props.serviceType, {
        id: props.id,
        status: 3,
        auditOpinion: auditOpinion.value,
      });
      message.success('审核通过成功！');
    } catch (error) {
      message.error('审核操作失败，请重试');
    }
  };

  // 审核拒绝
  const handleReject = async () => {
    if (!auditOpinion.value.trim()) {
      message.warning('拒绝审核时必须填写审核意见');
      return;
    }

    try {
      await reviewByServiceType(props.serviceType, {
        id: props.id,
        status: 4,
        auditOpinion: auditOpinion.value,
      });
      message.success('审核拒绝成功！');
    } catch (error) {
      message.error('审核操作失败，请重试');
    }
  };
</script>
```

## 状态码说明

- **status: 3** - 审核通过
- **status: 4** - 审核拒绝

## 注意事项

1. **竞价审核接口使用PUT请求**，其他审核接口使用POST请求
2. **auditOpinion字段为可选**，但建议在拒绝审核时填写具体原因
3. **所有接口都不需要区分增值委托和自主委托**，后端会自动处理
4. **错误处理**：建议在调用时添加try-catch进行错误处理
5. **权限控制**：确保当前用户有相应的审核权限

## 审核弹窗组件修改

审核弹窗组件已经更新为使用新的统一审核接口：

### 修改内容

1. **删除旧接口导入**：
   - 移除 `submitSelfAudit` 和 `submitAppreciationAudit` 的导入
   - 新增 `reviewByServiceType` 的导入

2. **修改审核提交逻辑**：

   ```typescript
   // 旧的方式（已删除）
   await submitSelfAudit({
     id: props.record.id,
     result: auditForm.result!,
     remark: auditForm.remark,
   });

   // 新的方式
   await reviewByServiceType(props.serviceType, {
     id: props.record.id,
     status: auditForm.result!,
     auditOpinion: auditForm.remark,
   });
   ```

3. **参数映射**：
   - `result` → `status`
   - `remark` → `auditOpinion`

### 受影响的组件

- `src/components/Audit/src/CommonSelfAudit.vue`
- `src/components/Audit/src/CommonAppreciationAudit.vue`

## 更新日志

- **v2.0**: 删除旧的审核接口，统一使用新的审核接口，审核弹窗组件已更新
- **v1.0**: 新增统一审核接口，支持资产处置、采购信息和竞价委托的审核操作
