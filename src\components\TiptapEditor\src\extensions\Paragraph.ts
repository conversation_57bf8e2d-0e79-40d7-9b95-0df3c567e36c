import { Extension } from '@tiptap/core'

export interface ParagraphOptions {
  HTMLAttributes: Record<string, any>
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    paragraphEnhanced: {
      /**
       * 设置段落缩进
       */
      setParagraphIndent: (indent: number) => ReturnType
      /**
       * 设置段落行距
       */
      setParagraphLineHeight: (lineHeight: string) => ReturnType
      /**
       * 设置段落间距
       */
      setParagraphSpacing: (spacing: string) => ReturnType
    }
  }
}

export default Extension.create<ParagraphOptions>({
  name: 'paragraphEnhanced',

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  addGlobalAttributes() {
    return [
      {
        types: ['paragraph'],
        attributes: {
          indent: {
            default: 0,
            parseHTML: element => {
              const style = element.getAttribute('style') || ''
              const match = style.match(/text-indent:\s*(\d+)em/)
              return match ? parseInt(match[1]) : 0
            },
            renderHTML: attributes => {
              if (!attributes.indent || attributes.indent === 0) {
                return {}
              }
              return {
                style: `text-indent: ${attributes.indent}em`,
              }
            },
          },
          lineHeight: {
            default: null,
            parseHTML: element => {
              const style = element.getAttribute('style') || ''
              const match = style.match(/line-height:\s*([\d.]+)/)
              return match ? match[1] : null
            },
            renderHTML: attributes => {
              if (!attributes.lineHeight) {
                return {}
              }
              return {
                style: `line-height: ${attributes.lineHeight}`,
              }
            },
          },
          spacing: {
            default: null,
            parseHTML: element => {
              const style = element.getAttribute('style') || ''
              const match = style.match(/margin-bottom:\s*([\d.]+px)/)
              return match ? match[1] : null
            },
            renderHTML: attributes => {
              if (!attributes.spacing) {
                return {}
              }
              return {
                style: `margin-bottom: ${attributes.spacing}`,
              }
            },
          },
        },
      },
    ]
  },

  addCommands() {
    return {
      setParagraphIndent: (indent: number) => ({ commands }) => {
        return commands.updateAttributes('paragraph', { indent })
      },
      setParagraphLineHeight: (lineHeight: string) => ({ commands }) => {
        return commands.updateAttributes('paragraph', { lineHeight })
      },
      setParagraphSpacing: (spacing: string) => ({ commands }) => {
        return commands.updateAttributes('paragraph', { spacing })
      },
    }
  },
})
