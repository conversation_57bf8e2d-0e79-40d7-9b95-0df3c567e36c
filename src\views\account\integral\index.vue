<template>
  <div class="p-4">
    <BasicTable @register="registerTable">
      <!-- 变化列 -->
      <template #change="{ record }">
        <span :class="record.change > 0 ? 'text-green-600' : 'text-red-600'"> {{ record.change > 0 ? '+' : '' }}{{ record.change }} </span>
      </template>

      <!-- 累计积分列 -->
      <template #totalPoints="{ text }">
        <span class="font-medium">{{ text.toLocaleString() }}</span>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup name="AccountIntegral">
  import { BasicTable, useTable } from '/@/components/Table';
  import { BasicColumn } from '/@/components/Table';
  import { formatToDateTime } from '/@/utils/dateUtil';

  // 表格列定义
  const columns: BasicColumn[] = [
    {
      title: '积分途径',
      dataIndex: 'source',
      width: 200,
      ellipsis: true,
    },
    {
      title: '变化',
      dataIndex: 'change',
      width: 120,
      slots: { customRender: 'change' },
    },
    {
      title: '累计积分',
      dataIndex: 'totalPoints',
      width: 120,
      slots: { customRender: 'totalPoints' },
    },
    {
      title: '时间',
      dataIndex: 'createTime',
      width: 160,
      customRender: ({ text }) => formatToDateTime(text),
    },
  ];

  // 模拟API调用
  async function queryIntegralList(params: any) {
    // 模拟假数据
    const mockData = [
      {
        id: 1,
        source: '完成资产处置委托',
        change: 500,
        totalPoints: 16546,
        createTime: '2024-12-09 10:30:00',
      },
      {
        id: 2,
        source: '参与竞价活动',
        change: 200,
        totalPoints: 16046,
        createTime: '2024-12-09 09:15:00',
      },
      {
        id: 3,
        source: '积分兑换商品',
        change: -300,
        totalPoints: 15846,
        createTime: '2024-12-08 16:45:00',
      },
      {
        id: 4,
        source: '每日签到',
        change: 10,
        totalPoints: 16146,
        createTime: '2024-12-08 14:20:00',
      },
      {
        id: 5,
        source: '推荐新用户',
        change: 1000,
        totalPoints: 16136,
        createTime: '2024-12-07 11:30:00',
      },
      {
        id: 6,
        source: '完成实名认证',
        change: 100,
        totalPoints: 15136,
        createTime: '2024-12-06 15:20:00',
      },
      {
        id: 7,
        source: '积分过期扣除',
        change: -50,
        totalPoints: 15036,
        createTime: '2024-12-05 00:00:00',
      },
    ];

    // 模拟分页
    const { current = 1, size = 10 } = params;
    const start = (current - 1) * size;
    const end = start + size;
    const records = mockData.slice(start, end);

    return Promise.resolve({
      records,
      total: mockData.length,
      current,
      size,
    });
  }

  // 快捷时间选项
  const quickTimeOptions = [
    { label: '今天', value: 'today' },
    { label: '近7日', value: 'week' },
    { label: '近1月', value: 'month' },
  ];

  // 处理快捷时间选择
  function handleQuickTime(value: string) {
    const now = new Date();
    let startTime: Date;
    let endTime = new Date(now);

    switch (value) {
      case 'today':
        startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'week':
        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startTime = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
        break;
      default:
        return;
    }

    // 设置表单时间区间值
    const form = getForm();
    form.setFieldsValue({
      timeRange: [startTime, endTime],
    });

    // 重新加载数据
    reload();
  }

  // 表格配置
  const [registerTable, { reload, getForm }] = useTable({
    api: queryIntegralList,
    columns,
    striped: false,
    useSearchForm: true,
    showTableSetting: false,
    bordered: false,
    showIndexColumn: false,
    canResize: true,
    inset: true,
    formConfig: {
      labelWidth: 80,
      size: 'large',
      schemas: [
        {
          field: 'quickTime',
          label: '',
          component: 'RadioGroup',
          componentProps: {
            options: quickTimeOptions,
            onChange: (e: any) => handleQuickTime(e.target.value),
            optionType: 'button',
            buttonStyle: 'solid',
            size: 'small',
          },
          colProps: { span: 6 },
        },
        {
          field: 'timeRange',
          label: '时间区间',
          component: 'RangePicker',
          componentProps: {
            showTime: true,
            format: 'YYYY-MM-DD HH:mm:ss',
          },
          colProps: { span: 12 },
        },
      ],
    },
  });
</script>

<style lang="less" scoped>
  .font-medium {
    font-weight: 500;
  }

  .text-green-600 {
    color: #16a34a;
  }

  .text-red-600 {
    color: #dc2626;
  }

  // 快捷时间按钮样式
  :deep(.ant-radio-group) {
    background: #f2f2f2;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 3px;
    white-space: nowrap;
    display: inline-flex;

    .ant-radio-button-wrapper {
      height: 36px !important;
      line-height: 36px !important;
      padding: 0 20px !important;
      font-size: 14px !important;
      background: transparent !important;
      color: #999 !important;
      border: none !important;
      border-left: none !important;
      margin-right: 0 !important;
      border-radius: 4px !important;

      &:first-child {
        border-radius: 4px !important;
        border-left: none !important;
      }

      &:last-child {
        border-radius: 4px !important;
      }

      &:hover {
        background: transparent !important;
        color: #999 !important;
      }

      &.ant-radio-button-wrapper-checked {
        background: #fff !important;
        color: #333 !important;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
        border: none !important;
        border-left: none !important;
      }

      &.ant-radio-button-wrapper-checked:hover {
        background: #fff !important;
        color: #333 !important;
      }

      // 强制去掉所有边框
      &::before {
        display: none !important;
      }
    }
  }
</style>
