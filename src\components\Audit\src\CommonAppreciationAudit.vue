<template>
  <div class="appreciation-audit">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <a-spin size="large" tip="加载中..." />
    </div>

    <!-- 审核内容 -->
    <div v-else class="audit-container" :class="{ 'view-only-mode': props.viewOnly || isAudited }">
      <!-- 基本信息展示 -->
      <div class="info-section">
        <span class="section-title">基本信息</span>
        <div class="info-grid-four">
          <div class="info-item">
            <span class="label">委托单号：</span>
            <span class="value">{{ auditData?.hgyEntrustOrder?.id || auditData?.id || '-' }}</span>
          </div>

          <!-- 竞价委托基本信息 -->
          <template v-if="serviceType === 1">
            <div class="info-item">
              <span class="label">标的名称：</span>
              <span class="value">{{ auditData?.hgyAuctionItemTemp?.itemName || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">委托企业：</span>
              <span class="value">{{ auditData?.hgyEntrustOrder?.entrustCompanyName || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">联系人：</span>
              <span class="value">{{ auditData?.hgyEntrustOrder?.relationUser || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">联系电话：</span>
              <span class="value">{{ auditData?.hgyEntrustOrder?.relationPhone || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">拍卖日期：</span>
              <span class="value">{{ formatDateTime(auditData?.hgyAuctionItemTemp?.auctionDate) }}</span>
            </div>
          </template>

          <!-- 资产处置基本信息 -->
          <template v-else-if="serviceType === 2">
            <div class="info-item">
              <span class="label">资产名称：</span>
              <span class="value">{{ auditData?.hgyAssetEntrust?.assetName || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">委托企业：</span>
              <span class="value">{{ auditData?.hgyEntrustOrder?.entrustCompanyName || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">联系人：</span>
              <span class="value">{{ auditData?.hgyEntrustOrder?.relationUser || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">联系电话：</span>
              <span class="value">{{ auditData?.hgyEntrustOrder?.relationPhone || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">处置底价：</span>
              <span class="value">¥{{ formatPrice(auditData?.hgyAssetEntrust?.disposalPrice) }}</span>
            </div>
          </template>

          <!-- 采购信息基本信息 -->
          <template v-else-if="serviceType === 3">
            <div class="info-item">
              <span class="label">采购名称：</span>
              <span class="value">{{ auditData?.hgyProcurement?.noticeName || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">委托企业：</span>
              <span class="value">{{ auditData?.hgyEntrustOrder?.createBy_dictText || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">联系人：</span>
              <span class="value">{{ auditData?.hgyEntrustOrder?.relationUser || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">联系电话：</span>
              <span class="value">{{ auditData?.hgyEntrustOrder?.relationPhone || '-' }}</span>
            </div>
          </template>

          <!-- 通用信息 -->
          <div class="info-item">
            <span class="label">创建时间：</span>
            <span class="value">{{ formatDateTime(auditData?.hgyEntrustOrder?.createTime) }}</span>
          </div>
        </div>
      </div>

      <!-- 根据服务类型显示不同的详细信息 -->
      <!-- 竞价委托 -->
      <div v-if="serviceType === 1" class="detail-section">
        <span class="section-title">竞价委托详情</span>
        <div class="detail-content scrollable-content" :class="{ 'view-only': props.viewOnly }">
          <div class="detail-grid">
            <!-- 委托单信息 -->
            <div v-if="auditData?.hgyEntrustOrder" class="detail-group">
              <span class="group-title">委托单信息</span>
              <div class="info-grid">
                <div class="info-item">
                  <span class="label">委托企业：</span>
                  <span class="value">{{ auditData.hgyEntrustOrder.entrustCompanyName || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">受委托企业：</span>
                  <span class="value">{{ auditData.hgyEntrustOrder.onEntrustCompanyName || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">联系人：</span>
                  <span class="value">{{ auditData.hgyEntrustOrder.relationUser || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">联系电话：</span>
                  <span class="value">{{ auditData.hgyEntrustOrder.relationPhone || '-' }}</span>
                </div>
              </div>
            </div>

            <!-- 标的信息 -->
            <div v-if="auditData?.hgyAuctionItemTemp" class="detail-group">
              <span class="group-title">标的信息</span>
              <div class="info-grid">
                <div class="info-item">
                  <span class="label">标的名称：</span>
                  <span class="value">{{ auditData.hgyAuctionItemTemp.itemName || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">标的数量：</span>
                  <span class="value">{{ auditData.hgyAuctionItemTemp.quantity || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">保留价设置：</span>
                  <span class="value">{{ auditData.hgyAuctionItemTemp.hasReservePrice === 1 ? '是' : '否' }}</span>
                </div>
                <div v-if="auditData.hgyAuctionItemTemp.hasReservePrice === 1" class="info-item">
                  <span class="label">保留价：</span>
                  <span class="value">{{ auditData.hgyAuctionItemTemp.reservePrice || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">省份：</span>
                  <span class="value">{{ auditData.hgyAuctionItemTemp.province || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">城市：</span>
                  <span class="value">{{ auditData.hgyAuctionItemTemp.city || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">区县：</span>
                  <span class="value">{{ auditData.hgyAuctionItemTemp.district || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">详细地址：</span>
                  <span class="value">{{ auditData.hgyAuctionItemTemp.address || '-' }}</span>
                </div>
                <div v-if="auditData.hgyAuctionItemTemp.specialNotes" class="info-item full-width">
                  <span class="label">特殊说明：</span>
                  <span class="value">{{ auditData.hgyAuctionItemTemp.specialNotes }}</span>
                </div>
              </div>
            </div>

            <!-- 附件信息 -->
            <div v-if="auditData?.hgyAuctionItemTemp?.attachmentList?.length" class="detail-group">
              <h4 class="group-title">附件信息</h4>
              <div class="attachment-list">
                <div v-for="(attachment, index) in auditData.hgyAuctionItemTemp.attachmentList" :key="index" class="attachment-item">
                  <div class="attachment-info">
                    <span class="attachment-name">{{ attachment.fileName }}</span>
                    <span class="attachment-type">{{ attachment.fileType }}</span>
                  </div>
                  <div class="attachment-actions">
                    <a-button type="link" size="small" @click="viewAttachment(attachment)">查看</a-button>
                    <a-button type="link" size="small" @click="downloadAttachment(attachment)">下载</a-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 资产处置 -->
      <div v-else-if="serviceType === 2" class="detail-section">
        <span class="section-title">资产处置详情</span>
        <div class="detail-content scrollable-content" :class="{ 'view-only': props.viewOnly }">
          <div class="detail-grid">
            <!-- 委托单信息 -->
            <div v-if="auditData?.hgyEntrustOrder" class="detail-group">
              <span class="group-title">委托单信息</span>
              <div class="info-grid">
                <div class="info-item">
                  <span class="label">委托企业：</span>
                  <span class="value">{{ auditData.hgyEntrustOrder.entrustCompanyName || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">受委托企业：</span>
                  <span class="value">{{ auditData.hgyEntrustOrder.onEntrustCompanyName || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">联系人：</span>
                  <span class="value">{{ auditData.hgyEntrustOrder.relationUser || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">联系电话：</span>
                  <span class="value">{{ auditData.hgyEntrustOrder.relationPhone || '-' }}</span>
                </div>
              </div>
            </div>

            <!-- 资产处置信息 -->
            <div v-if="auditData?.hgyAssetEntrust" class="detail-group">
              <span class="group-title">资产处置信息</span>
              <div class="info-grid">
                <div class="info-item">
                  <span class="label">资产名称：</span>
                  <span class="value">{{ auditData.hgyAssetEntrust.assetName || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">资产编号：</span>
                  <span class="value">{{ auditData.hgyAssetEntrust.assetNo || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">资产类型：</span>
                  <span class="value">{{ getAssetTypeText(auditData.hgyAssetEntrust.assetType) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">资产数量：</span>
                  <span class="value">{{ auditData.hgyAssetEntrust.quantity || '-' }} {{ auditData.hgyAssetEntrust.unit || '' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">使用年限：</span>
                  <span class="value">{{ auditData.hgyAssetEntrust.serviceLife || '-' }}年</span>
                </div>
                <div class="info-item">
                  <span class="label">折旧程度：</span>
                  <span class="value">{{ auditData.hgyAssetEntrust.depreciationDegree || '-' }}%</span>
                </div>
                <div class="info-item">
                  <span class="label">当前状态：</span>
                  <span class="value">{{ getCurrentStatusText(auditData.hgyAssetEntrust.currentStatus) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">评估价值：</span>
                  <span class="value">¥{{ formatPrice(auditData.hgyAssetEntrust.appraisalValue) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">处置底价：</span>
                  <span class="value">¥{{ formatPrice(auditData.hgyAssetEntrust.disposalPrice) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">处置时间：</span>
                  <span class="value">{{
                    formatDateRange(auditData.hgyAssetEntrust.disposalStartTime, auditData.hgyAssetEntrust.disposalEndTime)
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="label">处置地址：</span>
                  <span class="value">{{ formatAddress(auditData.hgyAssetEntrust) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">付款方式：</span>
                  <span class="value">{{ getPaymentMethodText(auditData.hgyAssetEntrust.paymentMethod) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">是否含税：</span>
                  <span class="value">{{ auditData.hgyAssetEntrust.isTaxIncluded === 1 ? '是' : '否' }}</span>
                </div>
                <div v-if="auditData.hgyAssetEntrust.specialNotes" class="info-item full-width">
                  <span class="label">特殊说明：</span>
                  <span class="value">{{ auditData.hgyAssetEntrust.specialNotes }}</span>
                </div>
              </div>
            </div>

            <!-- 附件信息 -->
            <div v-if="auditData?.hgyAttachmentList?.length" class="detail-group">
              <span class="group-title">附件信息</span>
              <div class="attachment-list">
                <div v-for="(attachment, index) in auditData.hgyAttachmentList" :key="index" class="attachment-item">
                  <div class="attachment-info">
                    <span class="attachment-name">{{ attachment.fileName }}</span>
                    <span class="attachment-type">{{ attachment.fileType }}</span>
                  </div>
                  <div class="attachment-actions">
                    <a-button type="link" size="small" @click="viewAttachment(attachment)">查看</a-button>
                    <a-button type="link" size="small" @click="downloadAttachment(attachment)">下载</a-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 采购信息 -->
      <div v-else-if="serviceType === 3" class="detail-section">
        <span class="section-title">采购信息详情</span>
        <div class="detail-content">
          <div class="detail-grid">
            <!-- 采购信息 -->
            <div v-if="auditData?.hgyProcurement" class="detail-group">
              <h4 class="group-title">采购信息</h4>
              <div class="info-grid">
                <div class="info-item">
                  <span class="label">采购名称：</span>
                  <span class="value">{{ auditData.hgyProcurement.noticeName || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">省份：</span>
                  <span class="value">{{ auditData.hgyProcurement.province || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">城市：</span>
                  <span class="value">{{ auditData.hgyProcurement.city || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">区县：</span>
                  <span class="value">{{ auditData.hgyProcurement.district || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">详细地址：</span>
                  <span class="value">{{ auditData.hgyProcurement.address || '-' }}</span>
                </div>
                <div v-if="auditData.hgyProcurement.specialNotes" class="info-item full-width">
                  <span class="label">特殊说明：</span>
                  <span class="value">{{ auditData.hgyProcurement.specialNotes }}</span>
                </div>
              </div>
            </div>

            <!-- 附件信息 -->
            <div v-if="auditData?.hgyAttachmentList?.length" class="detail-group">
              <h4 class="group-title">附件信息</h4>
              <div class="attachment-list">
                <div v-for="(attachment, index) in auditData.hgyAttachmentList" :key="index" class="attachment-item">
                  <div class="attachment-info">
                    <span class="attachment-name">{{ attachment.fileName }}</span>
                    <span class="attachment-type">{{ attachment.fileType }}</span>
                  </div>
                  <div class="attachment-actions">
                    <a-button type="link" size="small" @click="viewAttachment(attachment)">查看</a-button>
                    <a-button type="link" size="small" @click="downloadAttachment(attachment)">下载</a-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 审核详情区域 -->
      <div v-if="isAudited" class="audit-details">
        <span class="section-title">审核详情</span>
        <div class="audit-info">
          <div class="audit-result">
            <span class="label">审核结果：</span>
            <a-tag :color="auditData?.hgyEntrustOrder?.status === 3 ? 'green' : 'red'">
              {{ auditData?.hgyEntrustOrder?.status === 3 ? '通过' : '拒绝' }}
            </a-tag>
          </div>
          <div v-if="auditData?.hgyEntrustOrder?.auditOpinion" class="audit-opinion">
            <span class="label">审核意见：</span>
            <span class="content">{{ auditData.hgyEntrustOrder.auditOpinion }}</span>
          </div>
        </div>
      </div>

      <!-- 审核操作区域 -->
      <div v-if="!props.viewOnly && !isAudited" class="audit-actions">
        <span class="section-title">审核操作</span>
        <div class="audit-form">
          <a-form ref="auditFormRef" :model="auditForm" :rules="auditRules" layout="vertical">
            <a-form-item label="审核结果" name="result" required>
              <a-radio-group v-model:value="auditForm.result">
                <a-radio :value="3">通过</a-radio>
                <a-radio :value="4">拒绝</a-radio>
              </a-radio-group>
            </a-form-item>

            <a-form-item label="审核意见" name="remark">
              <a-textarea v-model:value="auditForm.remark" placeholder="请输入审核意见" :rows="4" :max-length="500" show-count />
            </a-form-item>
          </a-form>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" :loading="submitting" @click="handleSubmit"> 确认审核 </a-button>
        </div>
      </div>
    </div>

    <!-- 文件预览组件 -->
    <FilePreview v-model:open="previewVisible" :file-info="currentFile" @close="closePreview" />
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue';
  import { message } from 'ant-design-vue';
  import type { FormInstance } from 'ant-design-vue';
  import type { AuditRecord } from '../types';
  import { formatToDateTime } from '/@/utils/dateUtil';
  import { getAuditDetailByType, reviewByServiceType } from '/@/api/manageCenter/audit';
  import dayjs from 'dayjs';
  import FilePreview from '/@/components/FilePreview/src/FilePreview.vue';
  import { useFilePreview } from '/@/components/FilePreview/src/useFilePreview';

  interface Props {
    record: AuditRecord | null;
    serviceType: number;
    viewOnly?: boolean; // 是否只查看详情，不显示审核操作
  }

  interface Emits {
    (e: 'auditSuccess'): void;
    (e: 'close'): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  const loading = ref(false);
  const submitting = ref(false);
  const auditFormRef = ref<FormInstance>();
  const auditData = ref<any>(null);

  // 文件预览相关
  const { visible: previewVisible, currentFile, openPreview, closePreview, isSupportPreview } = useFilePreview();

  // 审核表单
  const auditForm = reactive({
    result: undefined as number | undefined,
    remark: '',
  });

  // 表单验证规则
  const auditRules = {
    result: [{ required: true, message: '请选择审核结果', trigger: 'change' }],
    remark: [{ required: true, message: '请输入审核意见', trigger: 'change' }],
  };

  // 判断是否已审核（状态为3通过或4拒绝）
  const isAudited = computed(() => {
    const status = auditData.value?.hgyEntrustOrder?.status;
    return status === 3 || status === 4;
  });

  // 格式化时间
  function formatDateTime(time: string | undefined) {
    return time ? formatToDateTime(dayjs(time)) : '-';
  }

  // 获取审核详情数据
  async function fetchAuditData() {
    if (!props.record?.id) return;

    loading.value = true;
    try {
      // 根据委托类型和服务类型调用不同的API获取详细数据
      const response = await getAuditDetailByType(
        props.record.id,
        props.record.entrustType || 1, // 增值委托
        props.serviceType || 2 // 默认资产处置
      );
      auditData.value = response;
      console.log('获取到的增值委托审核详情:', response);
    } catch (error) {
      console.error('获取审核详情失败:', error);
      message.error('获取审核详情失败');
      // 如果API调用失败，使用传入的record数据作为备用
      auditData.value = props.record;
    } finally {
      loading.value = false;
    }
  }

  // 提交审核
  async function handleSubmit() {
    try {
      await auditFormRef.value?.validate();

      if (!props.record?.id) {
        message.error('委托单号不能为空');
        return;
      }

      submitting.value = true;

      // 根据服务类型调用对应的审核API
      await reviewByServiceType(props.serviceType, {
        id: props.record.id,
        status: auditForm.result!,
        auditOpinion: auditForm.remark,
      });

      message.success('审核提交成功');
      emit('auditSuccess');
    } catch (error) {
      console.error('审核提交失败:', error);
      if (error !== 'validation failed') {
        message.error('审核提交失败');
      }
    } finally {
      submitting.value = false;
    }
  }

  // 取消审核
  function handleCancel() {
    emit('close');
  }

  // 格式化资产类型
  function getAssetTypeText(type: number) {
    const typeMap = {
      1: '物资/设备',
      2: '机动车',
      3: '房产',
      4: '土地',
      5: '其他',
    };
    return typeMap[type] || '未知';
  }

  // 格式化当前状态
  function getCurrentStatusText(status: number) {
    const statusMap = {
      1: '良好',
      2: '一般',
      3: '较差',
      4: '报废',
    };
    return statusMap[status] || '未知';
  }

  // 格式化价格
  function formatPrice(price: number) {
    if (!price && price !== 0) return '-';
    return price.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
  }

  // 格式化日期范围
  function formatDateRange(startTime: string, endTime: string) {
    if (!startTime || !endTime) return '-';
    const start = new Date(startTime).toLocaleDateString('zh-CN');
    const end = new Date(endTime).toLocaleDateString('zh-CN');
    return `${start} 至 ${end}`;
  }

  // 格式化地址
  function formatAddress(data: any) {
    if (!data) return '-';
    const { province, city, district, address } = data;
    const parts = [province, city, district, address].filter(Boolean);
    return parts.length > 0 ? parts.join('') : '-';
  }

  // 格式化付款方式
  function getPaymentMethodText(method: number) {
    const methodMap = {
      1: '一次性付款',
      2: '分期付款',
      3: '其他',
    };
    return methodMap[method] || '未知';
  }

  // 查看附件
  function viewAttachment(attachment: any) {
    if (!attachment.filePath) {
      message.warning('附件路径不存在');
      return;
    }

    // 检查是否支持预览
    if (isSupportPreview(attachment.filePath)) {
      openPreview({
        fileName: attachment.fileName,
        filePath: attachment.filePath,
        fileType: attachment.filePath.split('.').pop(),
      });
    } else {
      // 不支持预览的文件直接下载
      downloadAttachment(attachment);
    }
  }

  // 下载附件
  function downloadAttachment(attachment: any) {
    if (attachment.filePath) {
      const link = document.createElement('a');
      link.href = attachment.filePath;
      link.download = attachment.fileName || '附件';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
      message.warning('附件路径不存在');
    }
  }

  onMounted(() => {
    fetchAuditData();
  });
</script>

<style lang="less" scoped>
  .appreciation-audit {
    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 300px;
    }

    .audit-container {
      .section-title {
        font-family: 'PingFang Bold', sans-serif;
        font-size: 18px;
        color: #333;
        margin-bottom: 10px;
        border-left: 4px solid #004c66;
        padding-left: 12px;
        display: block;
      }

      .group-title {
        font-family: 'PingFang Bold', sans-serif;
        font-size: 14px;
        font-weight: bold;
        color: #666;
        margin-bottom: 12px;
        display: block;
      }

      .info-section {
        margin-bottom: 10px;

        .info-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 16px;

          .info-item {
            display: flex;

            .label {
              font-weight: 500;
              color: #666;
              min-width: 80px;
            }

            .value {
              color: #333;
              flex: 1;
            }
          }
        }

        .info-grid-four {
          padding: 16px;
          background-color: #f8f9fa;
          border-radius: 6px;
          border: 1px solid #e9ecef;
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: 10px;

          .info-item {
            display: flex;

            .label {
              font-family: 'PingFang Bold';
              color: #666;
              min-width: 80px;
            }

            .value {
              color: #333;
              flex: 1;
            }
          }
        }
      }

      .scrollable-content {
        max-height: 280px;
        min-height: 200px;
        overflow-y: auto;
        padding-right: 8px;

        &.view-only {
          min-height: 320px;
        }

        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;

          &:hover {
            background: #a8a8a8;
          }
        }
      }

      .detail-section {
        margin-bottom: 10px;
        .detail-content {
          padding: 16px;
          background-color: #f8f9fa;
          border-radius: 6px;
          border: 1px solid #e9ecef;
          color: #666;

          .detail-grid {
            display: flex;
            flex-direction: column;
            gap: 20px;
          }

          .detail-group {
            padding: 16px;
            background: #fff;
            border-radius: 6px;
            border: 1px solid #e8e8e8;

            .group-title {
              margin: 0 0 12px 0;
              font-size: 14px;
              font-weight: 600;
              color: #262626;
            }

            .info-grid {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
              gap: 12px;

              .info-item {
                display: flex;
                align-items: flex-start;

                &.full-width {
                  grid-column: 1 / -1;
                  flex-direction: column;
                  gap: 4px;
                }

                .label {
                  min-width: 80px;
                  color: #666;
                  font-weight: 500;
                }

                .value {
                  color: #262626;
                  word-break: break-all;
                }
              }
            }
          }

          .attachment-list {
            display: flex;
            flex-direction: column;
            gap: 8px;

            .attachment-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 8px 12px;
              background: #fff;
              border: 1px solid #d9d9d9;
              border-radius: 4px;

              .attachment-info {
                display: flex;
                align-items: center;
                flex: 1;

                .attachment-name {
                  color: #262626;
                  font-weight: 500;
                  margin-right: 12px;
                }

                .attachment-type {
                  color: #666;
                  font-size: 12px;
                  padding: 2px 8px;
                  background: #f0f0f0;
                  border-radius: 12px;
                }
              }

              .attachment-actions {
                display: flex;
                gap: 8px;

                .ant-btn-link {
                  padding: 0 4px;
                  height: auto;
                  font-size: 12px;
                }
              }
            }
          }
        }
      }

      .audit-details {
        margin-top: 10px;

        .audit-info {
          padding: 16px;
          background-color: #f8f9fa;
          border-radius: 6px;
          border: 1px solid #e9ecef;
          .audit-result {
            margin-bottom: 12px;
            display: flex;
            align-items: center;

            .label {
              font-weight: 500;
              margin-right: 8px;
              color: #333;
            }
          }

          .audit-opinion {
            display: flex;
            align-items: flex-start;

            .label {
              font-weight: 500;
              margin-right: 8px;
              color: #333;
              white-space: nowrap;
            }

            .content {
              flex: 1;
              color: #666;
              line-height: 1.5;
              word-break: break-word;
            }
          }
        }
      }

      .audit-actions {
        .audit-form {
          margin-bottom: 10px;
        }

        .action-buttons {
          display: flex;
          justify-content: center;
          gap: 12px;
        }
      }

      // 查看模式下增加详情区域高度
      &.view-only-mode {
        .detail-section {
          .detail-content {
            min-height: 400px; // 增加最小高度

            .detail-grid {
              gap: 32px; // 增加间距
            }

            .detail-group {
              padding: 24px; // 增加内边距

              .info-grid,
              .info-grid-four {
                padding: 24px; // 增加网格内边距
                gap: 16px; // 增加网格间距
              }

              .info-item {
                min-height: 40px; // 增加项目最小高度

                &.full-width {
                  .rich-content {
                    min-height: 120px; // 增加富文本内容高度
                    padding: 16px; // 增加富文本内边距
                  }
                }
              }
            }
          }
        }

        .attachment-section {
          .attachment-list {
            gap: 16px; // 增加附件列表间距

            .attachment-item {
              padding: 16px; // 增加附件项内边距
            }
          }
        }
      }
    }
  }
</style>
