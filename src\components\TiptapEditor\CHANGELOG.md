# Tiptap富文本编辑器更新日志

## v1.2.0 - 2024-08-22

### 新增功能

#### 📝 段落格式功能

- **段落缩进**：支持0-4字符的首行缩进设置
- **行距调整**：提供单倍到3倍行距的精确控制
- **段落间距**：支持0-2倍的段落间距设置
- **智能应用**：自动应用到当前选中的段落或标题
- **实时预览**：格式设置立即生效，所见即所得

#### 🛠 技术改进

- **自定义扩展**：创建了ParagraphFormat扩展支持段落格式设置
- **命令系统**：完整的段落格式命令API
- **样式渲染**：支持CSS样式的正确解析和渲染

### 工具栏增强

- **段落格式组**：在插入图片和模板功能之间新增段落格式工具组
- **下拉菜单**：缩进、行距、间距均采用下拉菜单形式，操作更直观
- **图标优化**：使用合适的图标表示不同的段落格式功能

## v1.1.0 - 2024-07-29

### 新增功能

#### 🎨 字体和样式功能

- **系统字体**：支持宋体、黑体、微软雅黑、楷体、仿宋等中文字体
- **苹方字体系列**：苹方常规、苹方中等、苹方加粗、苹方特粗
- **DIN字体系列**：DIN常规、DIN加粗、DIN黑斜体
- **特殊字体**：方正综艺、优设标题黑等设计字体
- **英文字体**：Arial、Times New Roman、Courier New
- **字体大小**：支持12px-36px的常用字号选择
- **文字颜色**：提供24种常用颜色选择
- **背景高亮**：支持18种背景颜色，适合重点标记

#### 📷 图片上传功能

- **直接上传**：点击图片按钮直接选择文件上传
- **自动插入**：上传成功后自动插入到编辑器当前位置
- **错误处理**：完善的上传失败提示和处理
- **格式支持**：支持所有常见图片格式（jpg、png、gif、webp等）

#### 🛠 工具栏优化

- **下拉菜单**：字体、字号、颜色选择使用下拉菜单，点击触发更精确
- **颜色选择器**：直观的颜色面板，支持清除颜色
- **按钮状态**：实时显示当前选中的格式状态
- **响应式布局**：工具栏支持换行，适应不同屏幕尺寸
- **本地字体支持**：集成项目本地字体文件，包括苹方、DIN、方正综艺等专业字体

### 技术改进

#### 📦 扩展系统

- **自定义扩展**：创建了FontSize扩展支持字体大小设置
- **模块化设计**：所有功能都基于Tiptap扩展系统
- **类型安全**：完整的TypeScript类型定义

#### 🔧 API兼容性

- **向后兼容**：保持与原JEditor组件相同的API
- **表单集成**：完美集成到JeecgBoot表单系统
- **事件处理**：支持change、update:value等事件

### 使用示例

#### 基础使用

```vue
<template>
  <TiptapEditor v-model="content" height="400px" placeholder="请输入内容..." @change="handleChange" />
</template>
```

#### 表单中使用

```vue
<template>
  <BasicForm :schemas="schemas" />
</template>

<script setup>
  const schemas = [
    {
      field: 'content',
      component: 'JEditorTiptap',
      label: '内容',
      componentProps: {
        height: '300px',
        placeholder: '请输入内容...',
      },
    },
  ];
</script>
```

#### 替换现有JEditor

```vue
<!-- 原来的写法 -->
<JEditor v-model:value="content" />

<!-- 新的写法 -->
<JEditorTiptap v-model:value="content" />
```

### 功能对比

| 功能     | JEditor (TinyMCE) | JEditorTiptap (Tiptap)    |
| -------- | ----------------- | ------------------------- |
| 字体选择 | ❌                | ✅ 17种字体（含本地字体） |
| 字号设置 | ❌                | ✅ 9种字号                |
| 文字颜色 | ❌                | ✅ 24种颜色               |
| 背景高亮 | ❌                | ✅ 18种颜色               |
| 图片上传 | ❌ 仅URL          | ✅ 直接上传               |
| 下拉菜单 | 鼠标悬停          | ✅ 点击触发               |
| 体积大小 | ~500KB+           | ~100KB                    |
| 许可证   | GPL/商业          | MIT免费                   |
| Vue集成  | 第三方包装        | 原生支持                  |

### 配置选项

#### TiptapEditor Props

```typescript
interface Props {
  modelValue?: string; // 编辑器内容
  placeholder?: string; // 占位符文本
  height?: string; // 编辑器高度
  disabled?: boolean; // 是否禁用
  hideToolbar?: boolean; // 是否隐藏工具栏
  autoFocus?: boolean; // 是否自动聚焦
}
```

#### 事件

```typescript
interface Emits {
  (e: 'update:modelValue', value: string): void;
  (e: 'change', value: string): void;
}
```

### 已知问题

1. **图片大小限制**：默认继承系统上传配置，建议不超过10MB
2. **字体渲染**：部分字体在不同操作系统上可能显示不同
3. **移动端适配**：工具栏在小屏幕设备上可能需要滚动

### 下一步计划

- [ ] 添加表格支持
- [ ] 支持数学公式
- [ ] 添加代码块语法高亮
- [ ] 支持协作编辑
- [ ] 添加更多图片编辑功能（裁剪、缩放等）

### 迁移指南

从JEditor迁移到JEditorTiptap非常简单：

1. **组件替换**：将`JEditor`替换为`JEditorTiptap`
2. **API兼容**：所有现有属性和事件都保持兼容
3. **样式调整**：可能需要微调CSS样式以适应新的工具栏
4. **测试验证**：建议在测试环境中验证所有功能

### 技术支持

如有问题或建议，请联系开发团队或提交Issue。
