# TiptapEditor 模板功能使用指南

## 1. 模板功能概述

TiptapEditor 的模板功能为用户提供了预定义的文档模板，大大提高了内容创建的效率和标准化程度。该功能具有以下优势：

- **提高效率**：预设的模板结构让用户无需从零开始创建文档
- **标准化**：确保文档格式的一致性和专业性
- **业务适配**：针对拍卖行业提供专业的业务模板
- **灵活使用**：支持单一模板指定和多模板选择两种使用方式
- **智能提示**：编辑器为空时自动提示使用模板

## 2. 模板定义位置

所有模板定义位于 `src/utils/template/index.ts` 文件中，该文件包含：

```typescript
// 模板接口定义
export interface EditorTemplate {
  id: string; // 模板唯一标识
  name: string; // 模板显示名称
  content: string; // 模板HTML内容
  description?: string; // 模板描述（可选）
}

// 模板类型定义
export type TemplateType =
  | 'auction-notice' // 拍卖公告
  | 'auction-rules' // 拍卖须知
  | 'important-notice' // 重要声明
  | 'item-introduction' // 标的介绍
  | 'material-description' // 物资详细描述

// 预定义模板数组
export const DEFAULT_TEMPLATES: EditorTemplate[] = [
  // ... 模板定义
];
```

## 3. 使用方式详解

### 3.1 单一模板类型使用

通过 `templateType` 参数指定特定的模板类型，适用于明确知道要使用哪种模板的场景：

```vue
<template>
  <!-- 拍卖公告模板 -->
  <JEditorTiptap v-model:value="auctionNotice" template-type="auction-notice" placeholder="请输入拍卖公告内容..." />

  <!-- 拍卖须知模板 -->
  <JEditorTiptap v-model:value="auctionRules" template-type="auction-rules" placeholder="请输入拍卖须知内容..." />
</template>
```

**特点：**

- 编辑器为空且获得焦点时，自动提示使用指定模板
- 点击工具栏"使用模板"按钮时，直接应用指定模板（会确认是否清空当前内容）
- 无需用户选择，直接应用对应模板

### 3.2 多模板选择使用

通过 `templates` 数组参数提供多个模板供用户选择：

```vue
<template>
  <JEditorTiptap v-model:value="content" :templates="availableTemplates" placeholder="请输入内容..." />
</template>

<script setup>
  import { getAllTemplates } from '/@/utils/template';

  const availableTemplates = getAllTemplates();
  // 或者自定义模板列表
  const customTemplates = [
    {
      id: 'custom-1',
      name: '自定义模板',
      content: '<p>自定义内容...</p>',
      description: '这是一个自定义模板',
    },
  ];
</script>
```

**特点：**

- 编辑器为空且获得焦点时，提示使用模板
- 点击工具栏"使用模板"按钮时，显示模板选择对话框
- 用户可以从多个模板中选择合适的模板

### 3.3 混合使用场景

当同时提供 `templateType` 和 `templates` 参数时，优先级规则如下：

1. **自动提示优先级**：`templateType` > `templates`
2. **手动使用优先级**：`templateType` > `templates`

```vue
<template>
  <!-- templateType 优先生效 -->
  <JEditorTiptap v-model:value="content" template-type="auction-notice" :templates="allTemplates" />
</template>
```

## 4. API参数说明

### 4.1 templateType 参数

| 属性         | 类型                      | 默认值    | 说明             |
| ------------ | ------------------------- | --------- | ---------------- |
| templateType | TemplateType \| undefined | undefined | 指定单一模板类型 |

**可选值：**

- `'auction-notice'` - 拍卖公告
- `'auction-rules'` - 拍卖须知
- `'important-notice'` - 重要声明
- `'item-introduction'` - 标的介绍
- `'material-description'` - 物资详细描述

### 4.2 templates 参数

| 属性      | 类型             | 默认值 | 说明                     |
| --------- | ---------------- | ------ | ------------------------ |
| templates | EditorTemplate[] | []     | 模板数组，用于多模板选择 |

**EditorTemplate 接口：**

```typescript
interface EditorTemplate {
  id: string; // 模板唯一标识
  name: string; // 模板显示名称
  content: string; // 模板HTML内容
  description?: string; // 模板描述（可选）
}
```

## 5. 代码示例

### 5.1 Vue组件中的基础使用

```vue
<template>
  <div class="editor-container">
    <!-- 单一模板使用 -->
    <JEditorTiptap v-model:value="content1" template-type="auction-notice" height="400px" />

    <!-- 多模板选择 -->
    <JEditorTiptap v-model:value="content2" :templates="templates" height="400px" />
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import JEditorTiptap from '/@/components/Form/src/jeecg/components/JEditorTiptap.vue';
  import { getAllTemplates } from '/@/utils/template';

  const content1 = ref('');
  const content2 = ref('');
  const templates = getAllTemplates();
</script>
```

### 5.2 表单系统中的集成示例

```vue
<template>
  <BasicForm :schemas="formSchemas" @submit="handleSubmit" />
</template>

<script setup lang="ts">
  import { BasicForm } from '/@/components/Form';

  const formSchemas = [
    {
      field: 'auctionNotice',
      component: 'JEditorTiptap',
      label: '拍卖公告',
      required: true,
      componentProps: {
        templateType: 'auction-notice',
        height: '400px',
        placeholder: '请输入拍卖公告内容...',
      },
    },
    {
      field: 'auctionRules',
      component: 'JEditorTiptap',
      label: '拍卖须知',
      componentProps: {
        templateType: 'auction-rules',
        height: '350px',
      },
    },
  ];

  const handleSubmit = (values) => {
    console.log('表单数据:', values);
  };
</script>
```

### 5.3 不同业务场景的实际应用案例

#### 拍卖管理系统

```vue
<template>
  <div class="auction-management">
    <!-- 拍卖公告编辑 -->
    <a-card title="拍卖公告" class="mb-4">
      <JEditorTiptap v-model:value="auctionData.notice" template-type="auction-notice" height="500px" @change="handleNoticeChange" />
    </a-card>

    <!-- 拍卖须知编辑 -->
    <a-card title="拍卖须知" class="mb-4">
      <JEditorTiptap v-model:value="auctionData.rules" template-type="auction-rules" height="400px" />
    </a-card>

    <!-- 标的介绍编辑 -->
    <a-card title="标的介绍">
      <JEditorTiptap v-model:value="auctionData.itemIntro" template-type="item-introduction" height="450px" />
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { reactive } from 'vue';

  const auctionData = reactive({
    notice: '',
    rules: '',
    itemIntro: '',
  });

  const handleNoticeChange = (content: string) => {
    // 实时保存或其他业务逻辑
    console.log('拍卖公告内容变化:', content);
  };
</script>
```

## 6. 模板类型列表

| 模板类型     | 标识符                 | 用途说明                                         | 适用场景                     |
| ------------ | ---------------------- | ------------------------------------------------ | ---------------------------- |
| 拍卖公告     | `auction-notice`       | 标准拍卖公告格式，包含拍卖标的、时间、地点等信息 | 拍卖行发布拍卖活动公告       |
| 拍卖须知     | `auction-rules`        | 详细的拍卖规则和参与须知                         | 告知竞买人拍卖规则和注意事项 |
| 重要声明     | `important-notice`     | 重要事项声明，包含法律责任和风险提示             | 重要事项的法律声明和风险告知 |
| 标的介绍     | `item-introduction`    | 拍卖标的详细介绍，包含基本信息和技术参数         | 详细介绍拍卖标的物的信息     |
| 物资详细描述 | `material-description` | 物资设备的详细描述，包含清单和质量状况           | 批量物资或设备的详细说明     |

## 7. 注意事项

### 7.1 使用最佳实践

1. **选择合适的使用方式**
   - 明确业务场景时使用 `templateType`
   - 需要灵活选择时使用 `templates` 数组
   - 避免同时使用两种方式，除非有特殊需求

2. **模板内容定制**
   - 可以通过修改 `src/utils/template/index.ts` 来定制模板内容
   - 建议保持模板的结构化和标准化
   - 使用占位符（如 `[日期]`、`[姓名]` 等）方便用户填写

3. **性能考虑**
   - 模板内容会在组件初始化时加载
   - 避免在模板中包含过大的图片或复杂的样式
   - 大量模板时考虑按需加载

### 7.2 开发注意事项

1. **类型安全**

   ```typescript
   // 推荐：使用类型定义
   import type { TemplateType } from '/@/utils/template';
   const templateType: TemplateType = 'auction-notice';

   // 避免：使用字符串字面量
   const templateType = 'auction-notice'; // 可能出现拼写错误
   ```

2. **模板验证**

   ```typescript
   import { isValidTemplateType } from '/@/utils/template';

   const userInput = 'auction-notice';
   if (isValidTemplateType(userInput)) {
     // 安全使用
     setTemplateType(userInput);
   }
   ```

3. **错误处理**
   - 当指定的模板类型不存在时，组件会静默忽略
   - 建议在开发环境中添加警告日志
   - 生产环境中提供默认的错误处理机制

### 7.3 扩展和维护

1. **添加新模板**

   ```typescript
   // 在 src/utils/template/index.ts 中添加
   export type TemplateType = 'existing-types' | 'new-template-type'; // 新增类型

   export const DEFAULT_TEMPLATES: EditorTemplate[] = [
     // ... 现有模板
     {
       id: 'new-template-type',
       name: '新模板名称',
       content: '<p>新模板内容...</p>',
       description: '新模板描述',
     },
   ];
   ```

2. **模板版本管理**
   - 建议为模板内容添加版本标识
   - 重要模板变更时保持向后兼容
   - 考虑建立模板变更日志

3. **国际化支持**
   - 模板名称和描述支持国际化
   - 模板内容可以根据语言环境动态加载
   - 考虑不同地区的业务规范差异

---

## 相关文件

- **模板定义**：`src/utils/template/index.ts`
- **TiptapEditor组件**：`src/components/TiptapEditor/src/TiptapEditor.vue`
- **JEditorTiptap组件**：`src/components/Form/src/jeecg/components/JEditorTiptap.vue`
- **测试页面**：`src/views/demo/editor/tiptap-enhanced/index.vue`

## 更新日志

- **v1.2.0** - 新增模板功能，支持 `templateType` 和 `templates` 参数
- **v1.2.1** - 新增5种拍卖业务模板
- **v1.2.2** - 优化模板使用体验，添加确认机制
- **v1.2.3** - 移除商务信函、会议纪要、项目报告模板，专注于拍卖业务场景
