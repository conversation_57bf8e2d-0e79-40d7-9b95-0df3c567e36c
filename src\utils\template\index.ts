/**
 * 富文本编辑器模板定义
 */

export interface EditorTemplate {
  id: string;
  name: string;
  content: string;
  description?: string;
}

/**
 * 模板类型定义
 */
export type TemplateType =
  | 'auction-notice' // 拍卖公告
  | 'auction-rules' // 拍卖须知
  | 'important-notice' // 重要声明
  | 'item-introduction' // 标的介绍
  | 'material-description'; // 物资详细描述

/**
 * 预定义的富文本模板
 */
export const DEFAULT_TEMPLATES: EditorTemplate[] = [
  // 新增的5种特定业务模板
  {
    id: 'auction-notice',
    name: '拍卖公告',
    description: '标准拍卖公告格式',
    content: `
      <h1 style="text-align: center; margin-bottom: 30px; font-size: 24px; font-weight: bold;">拍卖公告</h1>
      <p style="text-indent: 2em; line-height: 1.8; margin-bottom: 16px;">受委托，我公司定于<strong>[拍卖日期]</strong>在<strong>[拍卖地点]</strong>公开拍卖以下标的：</p>

      <h3 style="margin-top: 24px; margin-bottom: 16px;">一、拍卖标的</h3>
      <p style="text-indent: 2em; line-height: 1.8; margin-bottom: 16px;">[标的名称及详细描述]</p>

      <h3 style="margin-top: 24px; margin-bottom: 16px;">二、拍卖时间</h3>
      <p style="text-indent: 2em; line-height: 1.8; margin-bottom: 16px;"><strong>拍卖时间：</strong>[YYYY年MM月DD日 HH:MM]</p>
      <p style="text-indent: 2em; line-height: 1.8; margin-bottom: 16px;"><strong>展示时间：</strong>[展示开始时间] 至 [展示结束时间]</p>

      <h3 style="margin-top: 24px; margin-bottom: 16px;">三、拍卖地点</h3>
      <p style="text-indent: 2em; line-height: 1.8; margin-bottom: 16px;">[详细地址]</p>

      <h3 style="margin-top: 24px; margin-bottom: 16px;">四、参拍条件</h3>
      <ol style="padding-left: 2em; line-height: 1.8;">
        <li style="margin-bottom: 8px;">具有完全民事行为能力的自然人、法人和其他组织；</li>
        <li style="margin-bottom: 8px;">按规定交纳保证金；</li>
        <li style="margin-bottom: 8px;">遵守拍卖规则和相关法律法规。</li>
      </ol>

      <h3 style="margin-top: 24px; margin-bottom: 16px;">五、联系方式</h3>
      <p style="text-indent: 2em; line-height: 1.8; margin-bottom: 16px;"><strong>联系人：</strong>[联系人姓名]</p>
      <p style="text-indent: 2em; line-height: 1.8; margin-bottom: 16px;"><strong>联系电话：</strong>[联系电话]</p>
      <p style="text-indent: 2em; line-height: 1.8; margin-bottom: 16px;"><strong>地址：</strong>[公司地址]</p>

      <p style="text-align: right; margin-top: 40px;">[拍卖公司名称]<br>[发布日期]</p>
    `,
  },
  {
    id: 'auction-rules',
    name: '拍卖须知',
    description: '拍卖活动须知和规则',
    content: `
      <h1 style="text-align: center; margin-bottom: 30px; font-size: 24px; font-weight: bold;">拍卖须知</h1>

      <h3 style="margin-bottom: 16px;">一、竞买人须知</h3>
      <ol style="padding-left: 2em; line-height: 1.8;">
        <li style="margin-bottom: 12px;"><strong>竞买登记：</strong>竞买人应在拍卖前办理竞买登记手续，提供有效身份证明文件。</li>
        <li style="margin-bottom: 12px;"><strong>保证金：</strong>竞买人须按规定交纳保证金，保证金数额为[保证金金额]。</li>
        <li style="margin-bottom: 12px;"><strong>竞买资格：</strong>具有完全民事行为能力的自然人、法人和其他组织均可参与竞买。</li>
      </ol>

      <h3 style="margin-top: 24px; margin-bottom: 16px;">二、拍卖规则</h3>
      <ol style="padding-left: 2em; line-height: 1.8;">
        <li style="margin-bottom: 12px;"><strong>竞价方式：</strong>采用增价拍卖方式，起拍价为[起拍价]。</li>
        <li style="margin-bottom: 12px;"><strong>加价幅度：</strong>每次加价不少于[加价幅度]。</li>
        <li style="margin-bottom: 12px;"><strong>成交确认：</strong>拍卖师落槌确认后，最高应价者即为买受人。</li>
        <li style="margin-bottom: 12px;"><strong>拍卖佣金：</strong>买受人应向拍卖人支付成交价[佣金比例]的佣金。</li>
      </ol>

      <h3 style="margin-top: 24px; margin-bottom: 16px;">三、付款和交割</h3>
      <ol style="padding-left: 2em; line-height: 1.8;">
        <li style="margin-bottom: 12px;">买受人应在拍卖成交后[付款期限]内付清全部款项。</li>
        <li style="margin-bottom: 12px;">买受人付清款项后，应及时办理标的物交割手续。</li>
        <li style="margin-bottom: 12px;">逾期付款的，每日按成交价的[违约金比例]支付违约金。</li>
      </ol>

      <h3 style="margin-top: 24px; margin-bottom: 16px;">四、其他事项</h3>
      <ol style="padding-left: 2em; line-height: 1.8;">
        <li style="margin-bottom: 12px;">竞买人应仔细查看拍卖标的，拍卖成交后不得以任何理由退货。</li>
        <li style="margin-bottom: 12px;">本次拍卖活动遵循《中华人民共和国拍卖法》等相关法律法规。</li>
        <li style="margin-bottom: 12px;">拍卖人对本次拍卖活动拥有最终解释权。</li>
      </ol>

      <p style="text-align: right; margin-top: 40px;">[拍卖公司名称]<br>[发布日期]</p>
    `,
  },
  {
    id: 'important-notice',
    name: '重要声明',
    description: '重要事项声明模板',
    content: `
      <h1 style="text-align: center; margin-bottom: 30px; font-size: 24px; font-weight: bold; color: #d32f2f;">重要声明</h1>

      <p style="text-indent: 2em; line-height: 1.8; margin-bottom: 20px; font-size: 16px;">为确保各方权益，现就以下事项郑重声明：</p>

      <h3 style="margin-top: 24px; margin-bottom: 16px; color: #d32f2f;">一、标的物现状</h3>
      <ol style="padding-left: 2em; line-height: 1.8;">
        <li style="margin-bottom: 12px;">本次拍卖标的物以现状为准，不承担瑕疵担保责任。</li>
        <li style="margin-bottom: 12px;">竞买人应在拍卖前充分了解标的物状况，自行承担相关风险。</li>
        <li style="margin-bottom: 12px;">标的物的一切权利、义务及风险自成交确认后转移给买受人。</li>
      </ol>

      <h3 style="margin-top: 24px; margin-bottom: 16px; color: #d32f2f;">二、法律责任</h3>
      <ol style="padding-left: 2em; line-height: 1.8;">
        <li style="margin-bottom: 12px;">竞买人应具备完全民事行为能力，对自己的竞买行为承担法律责任。</li>
        <li style="margin-bottom: 12px;">恶意串通、虚假竞买等违法行为将承担相应法律后果。</li>
        <li style="margin-bottom: 12px;">买受人应按时履行付款义务，逾期将承担违约责任。</li>
      </ol>

      <h3 style="margin-top: 24px; margin-bottom: 16px; color: #d32f2f;">三、特别提醒</h3>
      <ol style="padding-left: 2em; line-height: 1.8;">
        <li style="margin-bottom: 12px;"><strong style="color: #d32f2f;">请仔细阅读拍卖须知和相关法律文件。</strong></li>
        <li style="margin-bottom: 12px;"><strong style="color: #d32f2f;">参与竞买即视为同意本声明的全部内容。</strong></li>
        <li style="margin-bottom: 12px;"><strong style="color: #d32f2f;">如有疑问，请在拍卖前咨询相关专业人士。</strong></li>
      </ol>

      <p style="text-align: center; margin-top: 40px; font-size: 16px; font-weight: bold;">本声明具有法律效力，请各方严格遵守！</p>

      <p style="text-align: right; margin-top: 40px;">[拍卖公司名称]<br>[发布日期]</p>
    `,
  },
  {
    id: 'item-introduction',
    name: '标的介绍',
    description: '拍卖标的详细介绍模板',
    content: `
      <h1 style="text-align: center; margin-bottom: 30px; font-size: 24px; font-weight: bold;">标的介绍</h1>

      <h3 style="margin-bottom: 16px;">基本信息</h3>
      <table border="1" style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
        <tr>
          <td style="padding: 12px; background-color: #f5f5f5; width: 20%; font-weight: bold;">标的名称</td>
          <td style="padding: 12px;">[标的名称]</td>
        </tr>
        <tr>
          <td style="padding: 12px; background-color: #f5f5f5; font-weight: bold;">标的编号</td>
          <td style="padding: 12px;">[标的编号]</td>
        </tr>
        <tr>
          <td style="padding: 12px; background-color: #f5f5f5; font-weight: bold;">起拍价</td>
          <td style="padding: 12px; color: #d32f2f; font-weight: bold;">[起拍价] 元</td>
        </tr>
        <tr>
          <td style="padding: 12px; background-color: #f5f5f5; font-weight: bold;">保证金</td>
          <td style="padding: 12px; color: #1976d2; font-weight: bold;">[保证金] 元</td>
        </tr>
        <tr>
          <td style="padding: 12px; background-color: #f5f5f5; font-weight: bold;">加价幅度</td>
          <td style="padding: 12px;">[加价幅度] 元</td>
        </tr>
      </table>

      <h3 style="margin-top: 24px; margin-bottom: 16px;">详细描述</h3>
      <p style="text-indent: 2em; line-height: 1.8; margin-bottom: 16px;">[标的物详细描述，包括规格、型号、数量、质量状况等]</p>

      <h3 style="margin-top: 24px; margin-bottom: 16px;">技术参数</h3>
      <ul style="padding-left: 2em; line-height: 1.8;">
        <li style="margin-bottom: 8px;"><strong>规格型号：</strong>[规格型号]</li>
        <li style="margin-bottom: 8px;"><strong>生产厂家：</strong>[生产厂家]</li>
        <li style="margin-bottom: 8px;"><strong>生产日期：</strong>[生产日期]</li>
        <li style="margin-bottom: 8px;"><strong>技术状态：</strong>[技术状态描述]</li>
      </ul>

      <h3 style="margin-top: 24px; margin-bottom: 16px;">存放地点</h3>
      <p style="text-indent: 2em; line-height: 1.8; margin-bottom: 16px;"><strong>地址：</strong>[详细存放地址]</p>
      <p style="text-indent: 2em; line-height: 1.8; margin-bottom: 16px;"><strong>联系人：</strong>[联系人] &nbsp;&nbsp;<strong>联系电话：</strong>[联系电话]</p>

      <h3 style="margin-top: 24px; margin-bottom: 16px;">特别说明</h3>
      <ol style="padding-left: 2em; line-height: 1.8;">
        <li style="margin-bottom: 8px;">标的物以现状为准，竞买人应实地查看。</li>
        <li style="margin-bottom: 8px;">买受人自行承担标的物的运输、保险等费用。</li>
        <li style="margin-bottom: 8px;">如有其他特殊情况，请咨询拍卖公司。</li>
      </ol>

      <p style="text-align: right; margin-top: 40px;">[拍卖公司名称]<br>[发布日期]</p>
    `,
  },
  {
    id: 'material-description',
    name: '物资详细描述',
    description: '物资设备详细描述模板',
    content: `
      <h1 style="text-align: center; margin-bottom: 30px; font-size: 24px; font-weight: bold;">物资详细描述</h1>

      <h3 style="margin-bottom: 16px;">物资概况</h3>
      <p style="text-indent: 2em; line-height: 1.8; margin-bottom: 16px;">本批物资包括[物资类别]，总计[数量]件/套，现详细描述如下：</p>

      <h3 style="margin-top: 24px; margin-bottom: 16px;">清单明细</h3>
      <table border="1" style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
        <tr style="background-color: #f5f5f5;">
          <th style="padding: 12px; text-align: center;">序号</th>
          <th style="padding: 12px; text-align: center;">物资名称</th>
          <th style="padding: 12px; text-align: center;">规格型号</th>
          <th style="padding: 12px; text-align: center;">数量</th>
          <th style="padding: 12px; text-align: center;">单位</th>
          <th style="padding: 12px; text-align: center;">状况</th>
        </tr>
        <tr>
          <td style="padding: 12px; text-align: center;">1</td>
          <td style="padding: 12px;">[物资名称1]</td>
          <td style="padding: 12px;">[规格型号1]</td>
          <td style="padding: 12px; text-align: center;">[数量1]</td>
          <td style="padding: 12px; text-align: center;">[单位1]</td>
          <td style="padding: 12px;">[状况描述1]</td>
        </tr>
        <tr>
          <td style="padding: 12px; text-align: center;">2</td>
          <td style="padding: 12px;">[物资名称2]</td>
          <td style="padding: 12px;">[规格型号2]</td>
          <td style="padding: 12px; text-align: center;">[数量2]</td>
          <td style="padding: 12px; text-align: center;">[单位2]</td>
          <td style="padding: 12px;">[状况描述2]</td>
        </tr>
      </table>

      <h3 style="margin-top: 24px; margin-bottom: 16px;">质量状况</h3>
      <ol style="padding-left: 2em; line-height: 1.8;">
        <li style="margin-bottom: 12px;"><strong>整体状况：</strong>[整体质量状况描述]</li>
        <li style="margin-bottom: 12px;"><strong>使用年限：</strong>[使用年限或购置时间]</li>
        <li style="margin-bottom: 12px;"><strong>维护情况：</strong>[维护保养情况]</li>
        <li style="margin-bottom: 12px;"><strong>功能状态：</strong>[功能完好程度]</li>
      </ol>

      <h3 style="margin-top: 24px; margin-bottom: 16px;">技术资料</h3>
      <ul style="padding-left: 2em; line-height: 1.8;">
        <li style="margin-bottom: 8px;">产品说明书：[有/无]</li>
        <li style="margin-bottom: 8px;">技术图纸：[有/无]</li>
        <li style="margin-bottom: 8px;">质量证书：[有/无]</li>
        <li style="margin-bottom: 8px;">其他资料：[其他相关技术资料]</li>
      </ul>

      <h3 style="margin-top: 24px; margin-bottom: 16px;">注意事项</h3>
      <ol style="padding-left: 2em; line-height: 1.8; color: #d32f2f;">
        <li style="margin-bottom: 12px;">所有物资均以现状交付，买受人应充分了解物资状况。</li>
        <li style="margin-bottom: 12px;">物资的搬运、运输由买受人自行负责，相关费用自理。</li>
        <li style="margin-bottom: 12px;">如需专业技术支持，建议买受人提前咨询相关专家。</li>
        <li style="margin-bottom: 12px;">物资交付后的安装、调试、维修等均由买受人承担。</li>
      </ol>

      <p style="text-align: right; margin-top: 40px;">[拍卖公司名称]<br>[发布日期]</p>
    `,
  },
];

/**
 * 获取所有模板
 */
export function getAllTemplates(): EditorTemplate[] {
  return DEFAULT_TEMPLATES;
}

/**
 * 根据ID获取模板
 */
export function getTemplateById(id: string): EditorTemplate | undefined {
  return DEFAULT_TEMPLATES.find((template) => template.id === id);
}

/**
 * 获取模板选项（用于下拉选择）
 */
export function getTemplateOptions(): Array<{ label: string; value: string; description?: string }> {
  return DEFAULT_TEMPLATES.map((template) => ({
    label: template.name,
    value: template.id,
    description: template.description,
  }));
}

/**
 * 根据模板类型获取模板
 */
export function getTemplateByType(templateType: TemplateType): EditorTemplate | undefined {
  return DEFAULT_TEMPLATES.find((template) => template.id === templateType);
}

/**
 * 检查模板类型是否有效
 */
export function isValidTemplateType(templateType: string): templateType is TemplateType {
  return DEFAULT_TEMPLATES.some((template) => template.id === templateType);
}

/**
 * 获取所有可用的模板类型
 */
export function getAvailableTemplateTypes(): TemplateType[] {
  return DEFAULT_TEMPLATES.map((template) => template.id as TemplateType);
}

/**
 * 创建自定义模板
 */
export function createCustomTemplate(template: Omit<EditorTemplate, 'id'>): EditorTemplate {
  return {
    id: `custom-${Date.now()}`,
    ...template,
  };
}
