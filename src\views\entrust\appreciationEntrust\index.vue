<template>
  <div class="appreciation-entrust">
    <!-- 步骤条 -->
    <div class="steps-wrapper">
      <Steps :current="currentStep" :steps="stepsList" @change="handleStepChange" />
    </div>

    <!-- 步骤内容 -->
    <div class="step-content">
      <!-- 第一步：编辑委托 -->
      <div v-if="currentStep === 0">
        <Step1
          ref="step1Ref"
          v-model="stepOneData"
          :location-loading="locationLoading"
          :is-edit-mode="isEditMode"
          @area-change="handleAreaChange"
          @get-current-location="getCurrentLocation"
          @service-type-change="handleServiceTypeChange"
        />

        <!-- 操作按钮 -->
        <div class="step-actions">
          <a-button type="primary" size="large" class="next-btn" @click="nextStep"> 下一步 </a-button>
        </div>
      </div>

      <!-- 第二步：发布委托信息 -->
      <div v-if="currentStep === 1">
        <Step2 ref="step2Ref" v-model="stepTwoData" />

        <!-- 操作按钮 -->
        <div class="step-actions">
          <!-- 编辑模式下根据状态显示不同按钮 -->
          <template v-if="isEditMode">
            <!-- 如果是草稿状态，显示保存草稿和确认发布按钮 -->
            <template v-if="currentStatus === 1">
              <a-button size="large" class="draft-btn" @click="submitForm(1)"> 保存至草稿 </a-button>
              <a-button type="primary" size="large" class="submit-btn" :loading="submitting" @click="submitForm(2)"> 确认发布 </a-button>
            </template>
            <!-- 如果不是草稿状态，显示保存修改按钮 -->
            <template v-else>
              <a-button type="primary" size="large" class="submit-btn" :loading="submitting" @click="submitForm(2)"> 保存修改 </a-button>
            </template>
          </template>
          <!-- 新增模式下显示原有按钮 -->
          <template v-else>
            <a-button size="large" class="draft-btn" @click="submitForm(1)"> 保存至草稿 </a-button>
            <a-button type="primary" size="large" class="submit-btn" :loading="submitting" @click="submitForm(2)"> 确认发布 </a-button>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref, onMounted } from 'vue';
  import { message } from 'ant-design-vue';
  import { useRouter, useRoute } from 'vue-router';
  import Steps from '@/components/Steps/index.vue';
  import Step1 from './components/Step1.vue';
  import Step2 from './components/Step2.vue';
  // 引入拍卖相关API接口和类型定义
  import { addAuctionItemTemp, addAssetEntrust, addProcurement, getCompanyList } from '@/api/entrust/auction';
  import type { AddAuctionItemTempParams, ServiceTypeEnum } from '@/api/entrust/types';
  import type { CompanyInfo, CompanyListResponse } from '@/api/entrust/auction';
  // 引入委托详情API
  import { queryEntrustById } from '@/api/orderManage/entrustDispose';
  import type { EntrustDisposeRecord } from '@/api/orderManage/entrustDispose';
  // 引入委托竞价详情API
  import { queryOrderItemTempById } from '@/api/orderManage/entrustBidding';

  // Step1组件引用
  const step1Ref = ref<InstanceType<typeof Step1>>();
  // Step2组件引用
  const step2Ref = ref<InstanceType<typeof Step2>>();

  // 路由实例
  const router = useRouter();
  const route = useRoute();

  // 当前步骤
  const currentStep = ref(0);

  // 提交状态
  const submitting = ref(false);

  // 定位加载状态
  const locationLoading = ref(false);

  // 编辑状态
  const isEditMode = ref(false);
  const editId = ref<string>('');
  const itemTempId = ref<string>('');
  const currentStatus = ref<number>(2); // 当前记录状态：1-草稿，2-已提交

  // 企业信息列表
  const companyList = ref<CompanyInfo[]>([]);
  // 企业信息加载状态
  const companyLoading = ref(false);

  // 移除了省市区模拟数据，现在使用JAreaLinkage组件内置的数据

  // 步骤列表
  const stepsList = [
    {
      title: '编辑委托信息',
      description: '填写委托的基本信息',
    },
    {
      title: '发布委托信息',
      description: '填写联系人信息并发布',
    },
  ];

  // 第一步表单数据
  let stepOneData = reactive({
    // 服务类型 - 默认选择发布竞价委托
    serviceType: 1,

    // 委托信息
    entrustInfo: {
      title: '', // 委托单位（显示企业名称）
      type: '', // 受委托单位（显示企业名称）
      description: '', // 委托描述
      noticeName: '', // 公告名称（仅用于采购信息）
    },

    // 基本信息
    basicInfo: {
      // 发布竞价委托
      subjectName: '', // 标的名称
      subjectQuantity: '', // 标的数量
      measurementUnit: '', // 计量单位
      auctionDate: '', // 拍卖日期
      quantityFlag: 0, // 是否展示实际数量 0-否 1-是
      hasReservePrice: 'no', // 是否设置保留价
      reservePrice: undefined as number | undefined, // 保留价

      // 发布资产处置
      assetName: '', // 资产名称
      assetCode: '', // 资产编号
      assetQuantity: '', // 资产数量
      assetMeasurementUnit: '', // 资产计量单位
      assetQuantityFlag: 0, // 资产处置时的是否展示实际数量 0-否 1-是
      servicePayType: 1, // 支付方式 1-买方支付 2-卖方支付，默认卖方支付
    },

    // 存放位置
    location: {
      province: '110000', // 省份
      city: '110100', // 城市
      area: '110101', // 区域
      detailAddress: '', // 详细地址
      coordinates: {
        // 坐标信息
        latitude: '',
        longitude: '',
      },
    },

    // 资料上传
    materials: {
      images: '', // 标的图片
      attachments: '', // 附件上传
      entrustDocument: '', // 委托单上传
      specialNote: '', // 特殊说明
      coverImage: '', // 封面图片
    },

    // 附件列表（用于回显）
    hgyAttachmentList: [] as any[],
  });

  // 第二步表单数据
  let stepTwoData = reactive({
    // 联系人信息
    contactInfo: {
      contactName: '', // 联系姓名
      contactPhone: '', // 联系电话
    },
  });

  // 处理步骤变化
  const handleStepChange = (step: number) => {
    // 只允许点击当前步骤之前的步骤返回，当前步骤之后的步骤不能通过点击步骤条跳转
    if (step < currentStep.value) {
      currentStep.value = step;
    }
  };

  // 省市区变化处理
  const handleAreaChange = (value: any) => {
    // JAreaSelect组件会自动更新province、city、area字段
    // 这里可以添加额外的处理逻辑
    console.log('省市区变化:', value);
  };

  // 获取当前位置
  const getCurrentLocation = () => {
    locationLoading.value = true;

    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          // 模拟根据经纬度获取地址信息
          setTimeout(() => {
            stepOneData.location.detailAddress = '模拟获取的详细地址：北京市海淀区中关村大街1号';
            locationLoading.value = false;
            message.success('位置获取成功');
          }, 1000);
        },
        (error) => {
          locationLoading.value = false;
          message.error('位置获取失败，请手动输入地址');
        }
      );
    } else {
      locationLoading.value = false;
      message.error('浏览器不支持地理位置获取');
    }
  };

  // 服务类型变化处理
  const handleServiceTypeChange = (value: number) => {
    stepOneData.serviceType = value;
    // 清空基本信息
    stepOneData.basicInfo = {
      subjectName: '',
      subjectQuantity: '',
      measurementUnit: '',
      auctionDate: '',
      quantityFlag: 0, // 是否展示实际数量 0-否 1-是
      hasReservePrice: '',
      reservePrice: undefined, // 保留价
      assetName: '',
      assetCode: '',
      assetQuantity: '',
      assetMeasurementUnit: '',
      assetQuantityFlag: 0, // 资产处置时的是否展示实际数量 0-否 1-是
      servicePayType: 1, // 支付方式 1-买方支付 2-卖方支付，默认卖方支付
    };
    // 重置资料上传信息，确保数组结构正确
    stepOneData.materials = {
      images: '', // 标的图片
      attachments: '', // 附件上传
      entrustDocument: '', // 委托单上传
      specialNote: '', // 特殊说明
      coverImage: '', // 封面图片
    };
  };

  // 获取企业信息列表
  const fetchCompanyList = async () => {
    try {
      companyLoading.value = true;
      const result: CompanyListResponse = await getCompanyList();
      console.log('获取企业信息:', result);

      if (result && Array.isArray(result)) {
        companyList.value = result;

        // 自动填充委托单位和受委托单位名称（仅在非编辑模式下）
        if (!isEditMode.value) {
          const entrustCompany = result.find((item) => item.value !== '1003'); // 委托企业
          const entrusteeCompany = result.find((item) => item.value === '1003'); // 受委托企业

          if (entrustCompany) {
            stepOneData.entrustInfo.title = entrustCompany.text;
          }
          if (entrusteeCompany) {
            stepOneData.entrustInfo.type = entrusteeCompany.text;
          }
        }
      }
    } catch (error) {
      // console.error('获取企业信息失败:', error);
    } finally {
      companyLoading.value = false;
    }
  };

  // 获取委托详情数据
  const fetchEntrustDetail = async (id: string) => {
    try {
      let result;
      // 根据服务类型调用不同的接口
      if (stepOneData.serviceType === 1) {
        // 服务类型为1时，调用委托竞价详情接口
        result = await queryOrderItemTempById(id);
        // 处理委托竞价数据回显
        if (result) {
          await handleAuctionDataDisplay(result);
          return;
        }
      } else {
        // 服务类型为2时，调用原有的资产处置详情接口
        result = await queryEntrustById(id);
      }

      // 回显数据到表单（原有逻辑，用于服务类型为2的情况）
      if (result) {
        const { hgyAssetEntrust, hgyEntrustOrder, hgyAttachmentList } = result;

        // 回显第一步数据
        if (hgyAssetEntrust) {
          // 根据服务类型回显不同的字段
          if (stepOneData.serviceType === 1) {
            // 发布竞价委托
            stepOneData.basicInfo.subjectName = hgyAssetEntrust.assetName || '';
            stepOneData.basicInfo.subjectQuantity = hgyAssetEntrust.quantity?.toString() || '';
            stepOneData.basicInfo.measurementUnit = hgyAssetEntrust.unit || '';
            stepOneData.basicInfo.reservePrice = hgyAssetEntrust.disposalPrice || undefined;
            stepOneData.basicInfo.hasReservePrice = hgyAssetEntrust.disposalPrice ? '1' : '0';
          } else if (stepOneData.serviceType === 2) {
            // 发布资产处置
            stepOneData.basicInfo.assetName = hgyAssetEntrust.assetName || '';
            stepOneData.basicInfo.assetCode = hgyAssetEntrust.assetNo || '';
            stepOneData.basicInfo.assetQuantity = hgyAssetEntrust.quantity?.toString() || '';
            stepOneData.basicInfo.assetMeasurementUnit = hgyAssetEntrust.unit || '';
            stepOneData.basicInfo.assetQuantityFlag = hgyAssetEntrust.quantityFlag ?? 0; // 是否展示实际数量
            stepOneData.basicInfo.servicePayType = hgyAssetEntrust.servicePayType ?? 1; // 支付方式，默认买方支付
          }

          // 回显地址信息
          stepOneData.location.province = hgyAssetEntrust.provinceCode || '';
          stepOneData.location.city = hgyAssetEntrust.cityCode || '';
          stepOneData.location.area = hgyAssetEntrust.districtCode || '';
          stepOneData.location.detailAddress = hgyAssetEntrust.address || '';

          // 回显特殊说明
          stepOneData.materials.specialNote = hgyAssetEntrust.specialNotes || '';

          // 回显封面图片
          stepOneData.materials.coverImage = hgyAssetEntrust.coverImage || '';
        }

        // 回显委托信息
        if (hgyEntrustOrder) {
          stepOneData.entrustInfo.title = hgyEntrustOrder.entrustCompanyName || '';
          stepOneData.entrustInfo.type = hgyEntrustOrder.onEntrustCompanyName || '';

          // 设置当前记录状态
          currentStatus.value = hgyEntrustOrder.status || 2;

          // 回显联系人信息
          stepTwoData.contactInfo.contactName = hgyEntrustOrder.relationUser || '';
          stepTwoData.contactInfo.contactPhone = hgyEntrustOrder.relationPhone || '';
        }

        // 在编辑模式下，如果委托单位或受委托单位为空，则从企业信息列表中自动填充
        if (isEditMode.value && companyList.value.length > 0) {
          if (!stepOneData.entrustInfo.title) {
            const entrustCompany = companyList.value.find((item) => item.value !== '1003'); // 委托企业
            if (entrustCompany) {
              stepOneData.entrustInfo.title = entrustCompany.text;
            }
          }

          if (!stepOneData.entrustInfo.type) {
            const entrusteeCompany = companyList.value.find((item) => item.value === '1003'); // 受委托企业
            if (entrusteeCompany) {
              stepOneData.entrustInfo.type = entrusteeCompany.text;
            }
          }
        }

        // 回显附件信息
        if (hgyAttachmentList && Array.isArray(hgyAttachmentList) && hgyAttachmentList.length > 0) {
          stepOneData.hgyAttachmentList = hgyAttachmentList;
        }
      }
    } catch (error) {
      console.error('获取委托详情失败:', error);
    }
  };

  // 处理委托竞价数据回显（服务类型为1时使用）
  const handleAuctionDataDisplay = async (result: any) => {
    try {
      // 根据JJapi.txt说明，数据结构包含hgyEntrustOrder、hgyAuctionItemTemp两部分（增值委托不包含hgyAuction）
      if (result) {
        const { hgyEntrustOrder, hgyAuctionItemTemp } = result;
        itemTempId.value = hgyAuctionItemTemp.id || '';

        // 回显委托单信息
        if (hgyEntrustOrder) {
          stepOneData.entrustInfo.title = hgyEntrustOrder.entrustCompanyName || '';
          stepOneData.entrustInfo.type = hgyEntrustOrder.onEntrustCompanyName || '';

          // 设置当前记录状态
          currentStatus.value = hgyEntrustOrder.status || 2;

          // 回显联系人信息
          stepTwoData.contactInfo.contactName = hgyEntrustOrder.relationUser || '';
          stepTwoData.contactInfo.contactPhone = hgyEntrustOrder.relationPhone || '';
        }

        // 在编辑模式下，如果委托单位或受委托单位为空，则从企业信息列表中自动填充
        if (isEditMode.value && companyList.value.length > 0) {
          if (!stepOneData.entrustInfo.title) {
            const entrustCompany = companyList.value.find((item) => item.value !== '1003'); // 委托企业
            if (entrustCompany) {
              stepOneData.entrustInfo.title = entrustCompany.text;
            }
          }

          if (!stepOneData.entrustInfo.type) {
            const entrusteeCompany = companyList.value.find((item) => item.value === '1003'); // 受委托企业
            if (entrusteeCompany) {
              stepOneData.entrustInfo.type = entrusteeCompany.text;
            }
          }
        }

        // 回显拍卖标的临时信息
        if (hgyAuctionItemTemp) {
          // 根据服务类型回显不同的字段
          if (stepOneData.serviceType === 1) {
            // 发布竞价委托
            stepOneData.basicInfo.subjectName = hgyAuctionItemTemp.itemName || '';
            stepOneData.basicInfo.subjectQuantity = hgyAuctionItemTemp.quantity?.toString() || '';
            stepOneData.basicInfo.measurementUnit = hgyAuctionItemTemp.unit || '';
            stepOneData.basicInfo.quantityFlag = hgyAuctionItemTemp.quantityFlag || 0; // 是否展示实际数量
            stepOneData.basicInfo.hasReservePrice = hgyAuctionItemTemp.hasReservePrice ? '1' : '0';
            stepOneData.basicInfo.auctionDate = hgyAuctionItemTemp.auctionDate || '';
            if (hgyAuctionItemTemp.hasReservePrice === 1) {
              stepOneData.basicInfo.reservePrice = hgyAuctionItemTemp.reservePrice || undefined;
            }
          }

          // 回显地址信息
          stepOneData.location.province = hgyAuctionItemTemp.province || '';
          stepOneData.location.city = hgyAuctionItemTemp.city || '';
          stepOneData.location.area = hgyAuctionItemTemp.district || '';
          stepOneData.location.detailAddress = hgyAuctionItemTemp.address || '';

          // 回显特殊说明
          stepOneData.materials.specialNote = hgyAuctionItemTemp.specialNotes || '';

          // 回显附件信息
          if (hgyAuctionItemTemp.attachmentList && Array.isArray(hgyAuctionItemTemp.attachmentList) && hgyAuctionItemTemp.attachmentList.length > 0) {
            console.log('增值委托竞价开始处理附件回显:', hgyAuctionItemTemp.attachmentList);
            stepOneData.hgyAttachmentList = hgyAuctionItemTemp.attachmentList;
          }
        }
      }
    } catch (error) {
      console.error('增值委托竞价数据回显失败:', error);
    }
  };

  // 组件挂载时获取企业信息和处理编辑模式
  onMounted(async () => {
    // 先获取企业信息
    await fetchCompanyList();

    // 检查是否为编辑模式
    const id = route.query.id as string;
    const serviceType = route.query.serviceType as string;

    if (id) {
      isEditMode.value = true;
      editId.value = id;

      // 设置服务类型
      if (serviceType) {
        stepOneData.serviceType = parseInt(serviceType);
      }

      // 获取委托详情（在企业信息获取完成后）
      fetchEntrustDetail(id);
    }
  });

  // 下一步
  const nextStep = async () => {
    // 验证当前步骤的表单数据
    if (currentStep.value === 0) {
      // 第一步：调用Step1组件的表单校验
      const isValid = await step1Ref.value?.validateForm();
      if (!isValid) {
        return;
      }
    } else if (!validateCurrentStep()) {
      return;
    }

    if (currentStep.value < stepsList.length - 1) {
      currentStep.value++;
    }
  };

  // 验证当前步骤 - 已移除校验逻辑
  const validateCurrentStep = (): boolean => {
    // 暂时移除所有校验，直接返回true
    return true;
  };

  // 根据文件扩展名判断文件类型的函数
  const getFileTypeByExtension = (filePath: string): string => {
    if (!filePath) return 'other';

    const extension = filePath.toLowerCase().split('.').pop();

    switch (extension) {
      case 'pdf':
        return 'pdf';
      case 'ppt':
      case 'pptx':
        return 'ppt';
      case 'xls':
      case 'xlsx':
        return 'excel';
      case 'doc':
      case 'docx':
        return 'doc';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return 'image';
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
      case 'flv':
        return 'video';
      case 'mp3':
      case 'wav':
      case 'flac':
      case 'aac':
        return 'mp3';
      case 'zip':
      case 'rar':
      case '7z':
      case 'tar':
      case 'gz':
        return 'zip';
      default:
        return 'other';
    }
  };

  // 提交表单
  const submitForm = async (status: number) => {
    // 验证Step2表单数据
    const isStep2Valid = await step2Ref.value?.validateForm();
    if (!isStep2Valid) {
      return;
    }

    submitting.value = true;

    try {
      // 合并两步的数据
      const submitData = {
        ...stepOneData,
        ...stepTwoData,
      };

      // 根据企业名称获取对应的ID值
      const getCompanyIdByName = (companyName: string): string => {
        const company = companyList.value.find((item) => item.text === companyName);
        return company ? company.value : '';
      };

      // 获取封面图片路径的函数
      const getCoverImage = (): string => {
        // 优先从 materials.coverImage 字段获取封面图片
        if (submitData.materials.coverImage) {
          try {
            const coverImage =
              typeof submitData.materials.coverImage === 'string' ? JSON.parse(submitData.materials.coverImage) : submitData.materials.coverImage;
            if (Array.isArray(coverImage) && coverImage.length > 0) {
              return coverImage[0].filePath || '';
            } else if (typeof coverImage === 'string') {
              return coverImage;
            }
          } catch (e) {
            console.warn('解析封面图片数据失败:', e);
          }
        }

        // 如果没有专门的封面图片，则从 materials.images 中获取第一张图片作为封面图片
        if (submitData.materials.images) {
          try {
            const images = typeof submitData.materials.images === 'string' ? JSON.parse(submitData.materials.images) : submitData.materials.images;
            if (Array.isArray(images) && images.length > 0) {
              return images[0].filePath || '';
            }
          } catch (e) {
            console.warn('解析图片数据失败:', e);
          }
        }
        return '';
      };

      // 构造附件列表的通用函数
      const buildAttachmentList = (bizType: string) => {
        return [
          // 图片附件
          ...(() => {
            try {
              const images = typeof submitData.materials.images === 'string' ? JSON.parse(submitData.materials.images) : submitData.materials.images;
              return (Array.isArray(images) ? images : []).map((file: any) => ({
                bizType,
                fileName: file.fileName,
                filePath: file.filePath,
                fileSize: file.fileSize,
                fileType: getFileTypeByExtension(file.filePath),
              }));
            } catch (e) {
              console.warn('解析图片附件数据失败:', e);
              return [];
            }
          })(),
          // 其他附件
          ...(() => {
            try {
              const attachments =
                typeof submitData.materials.attachments === 'string'
                  ? JSON.parse(submitData.materials.attachments)
                  : submitData.materials.attachments;
              return (Array.isArray(attachments) ? attachments : []).map((file: any) => ({
                bizType,
                fileName: file.fileName,
                filePath: file.filePath,
                fileSize: file.fileSize,
                fileType: getFileTypeByExtension(file.filePath),
              }));
            } catch (e) {
              console.warn('解析其他附件数据失败:', e);
              return [];
            }
          })(),
          // 委托单附件
          ...(() => {
            try {
              const entrustDocument =
                typeof submitData.materials.entrustDocument === 'string'
                  ? JSON.parse(submitData.materials.entrustDocument)
                  : submitData.materials.entrustDocument;
              return (Array.isArray(entrustDocument) ? entrustDocument : []).map((file: any) => ({
                bizType,
                fileName: file.fileName,
                filePath: file.filePath,
                fileSize: file.fileSize,
                fileType: 'wtd',
              }));
            } catch (e) {
              console.warn('解析委托单附件数据失败:', e);
              return [];
            }
          })(),
        ];
      };

      let result;

      // 根据服务类型调用不同的接口
      if (submitData.serviceType === 1) {
        // 发布竞价委托
        const apiData: AddAuctionItemTempParams = {
          // 委托单信息
          hgyEntrustOrder: {
            id: isEditMode.value ? editId.value : undefined, // 修改时传id，新增时不传
            entrustType: 1, // 委托类型
            serviceType: submitData.serviceType, // 服务类型
            status: status, // 状态
            entrustCompanyId: getCompanyIdByName(submitData.entrustInfo.title), // 委托企业ID
            entrustCompanyName: submitData.entrustInfo.title, // 委托企业名称
            onEntrustCompanyId: getCompanyIdByName(submitData.entrustInfo.type), // 受委托企业ID
            onEntrustCompanyName: submitData.entrustInfo.type, // 受委托企业名称
            relationUser: submitData.contactInfo.contactName, // 联系人
            relationPhone: submitData.contactInfo.contactPhone, // 联系电话
          },
          // 拍卖标的临时信息
          hgyAuctionItemTemp: {
            id: isEditMode.value ? itemTempId.value : undefined, // 修改时传id，新增时不传
            itemName: submitData.basicInfo.subjectName, // 标的名称
            quantity: submitData.basicInfo.subjectQuantity, // 标的数量
            unit: submitData.basicInfo.measurementUnit, // 计量单位
            auctionDate: submitData.basicInfo.auctionDate || undefined, // 拍卖日期
            quantityFlag: submitData.basicInfo.quantityFlag, // 是否展示实际数量 0-否 1-是
            hasReservePrice: submitData.basicInfo.hasReservePrice === 'yes' ? 1 : 0, // 是否设置保留价
            reservePrice:
              submitData.basicInfo.hasReservePrice === 'yes' && submitData.basicInfo.reservePrice
                ? Number(submitData.basicInfo.reservePrice)
                : undefined, // 保留价
            province: submitData.location.province, // 省份
            city: submitData.location.city, // 城市
            district: submitData.location.area, // 区县
            address: submitData.location.detailAddress, // 详细地址
            specialNotes: submitData.materials.specialNote, // 特殊说明
            attachmentList: buildAttachmentList('WTJJ'), // 增值委托中发布竞价委托时的附件
          },
        };
        result = await addAuctionItemTemp(apiData);
      } else if (submitData.serviceType === 2) {
        // 发布资产处置
        const apiData = {
          id: isEditMode.value ? editId.value : undefined, // 修改时传id，新增时不传
          entrustCompanyId: Number(getCompanyIdByName(submitData.entrustInfo.title)), // 委托企业ID
          onEntrustCompanyId: Number(getCompanyIdByName(submitData.entrustInfo.type)), // 受委托企业ID
          entrustType: 1, // 委托类型(1-增值 2-自主)
          serviceType: submitData.serviceType, // 服务类型(1-竞价委托 2-资产处置 3-采购信息)
          status: status, // 状态(1-草稿 2-提交)
          assetName: submitData.basicInfo.assetName, // 资产名称
          assetNo: submitData.basicInfo.assetCode || undefined, // 资产编号
          assetType: 1, // 资产类型（暂时固定为1，可根据需要调整）
          quantity: submitData.basicInfo.assetQuantity, // 资产数量
          unit: submitData.basicInfo.assetMeasurementUnit, // 计量单位
          quantityFlag: submitData.basicInfo.assetQuantityFlag, // 是否展示实际数量 0-否 1-是
          servicePayType: submitData.basicInfo.servicePayType, // 支付方式 1-买方支付 2-卖方支付
          provinceCode: submitData.location.province, // 省份
          cityCode: submitData.location.city, // 城市编码
          districtCode: submitData.location.area, // 区县编码
          address: submitData.location.detailAddress, // 详细地址
          relationUser: submitData.contactInfo.contactName, // 联系人
          relationPhone: submitData.contactInfo.contactPhone, // 联系电话
          coverImage: getCoverImage(), // 封面图片：取标的图片集合中的第一项
          attachmentList: buildAttachmentList('WTCZ'), // 增值委托发布资产处置时的附件
        };
        console.log('发布资产处置数据:', apiData);
        result = await addAssetEntrust(apiData);
      } else if (submitData.serviceType === 3) {
        // 发布采购信息
        const apiData = {
          id: isEditMode.value ? editId.value : undefined, // 修改时传id，新增时不传
          entrustType: 1, // 委托类型(1-增值 2-自主)
          serviceType: submitData.serviceType, // 服务类型(1-竞价委托 2-资产处置 3-采购信息)
          status: status, // 状态(1-草稿 2-提交)
          noticeName: submitData.entrustInfo.noticeName, // 公告名称（使用独立的noticeName字段）
          unit: submitData.basicInfo.measurementUnit || submitData.basicInfo.assetMeasurementUnit, // 计量单位
          provinceCode: submitData.location.province, // 省份
          cityCode: submitData.location.city, // 城市编码
          districtCode: submitData.location.area, // 区县编码
          relationUser: submitData.contactInfo.contactName, // 联系人
          relationPhone: submitData.contactInfo.contactPhone, // 联系电话
          attachmentList: buildAttachmentList('WTCG'), // 增值委托发布采购委托时的附件
        };
        result = await addProcurement(apiData);
      }

      // 提交成功后的处理
      // 只有在确认发布(status=2)时才跳转，保存至草稿(status=1)不跳转
      if (status === 2) {
        // 根据服务类型跳转到对应的订单页面
        if (submitData.serviceType === 1) {
          // 发布竞价委托 -> 委托竞价页面
          router.push('/orderManage/entrustBidding');
        } else if (submitData.serviceType === 2) {
          // 发布资产处置 -> 委托处置页面
          router.push('/orderManage/entrustDispose');
        } else if (submitData.serviceType === 3) {
          // 发布采购信息 -> 采购信息页面
          router.push('/hgy/entrustService/hgyProcurementList');
        }
      }
    } catch (error) {
      console.error('委托发布失败:', error);
    } finally {
      submitting.value = false;
    }
  };
</script>

<style lang="less" scoped>
  // 步骤操作按钮样式
  .step-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    padding: 32px 0;
    margin-top: 24px;

    .ant-btn {
      width: 434px;
      height: 40px;
      border-radius: 6px;
      font-weight: 500;
      transition: all 0.3s ease;

      &.next-btn,
      &.submit-btn {
        box-shadow: 0 0 0;
        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
      }

      &.prev-btn {
        &:hover {
          border-color: #40a9ff;
          color: #40a9ff;
        }
      }
    }
  }

  // 主容器样式
  .appreciation-entrust {
    padding: 24px;
    background-color: #fff;
    height: calc(100vh - 168px); // 减去头部和底部的高度
    overflow-y: auto;
    margin: 19px 30px 30px 30px;
    border-radius: 10px;
  }

  // 步骤条包装器
  .steps-wrapper {
    margin-bottom: 32px;
    padding: 0 24px;
  }

  // 步骤内容
  .step-content {
    margin: 0 auto; // 居中显示
    padding: 0 24px;
  }

  // 步骤面板
  .step-panel {
    background-color: #fff;
    border-radius: 8px;
  }

  // 操作按钮区域
  .step-actions {
    display: flex;
    justify-content: center;
    gap: 16px; // 按钮之间的间距
    margin-top: 48px; // 与表单内容的间距
    padding-top: 24px;

    .prev-btn,
    .next-btn,
    .submit-btn {
      width: 434px; // 按钮最小宽度
      height: 48px; // 按钮高度
      font-size: 16px; // 字体大小
      border-radius: 6px; // 圆角
    }

    .next-btn,
    .submit-btn {
      background-color: #004c66; // 主色调
      border-color: #004c66;

      &:hover:not(:disabled) {
        background: rgba(0, 76, 102, 0.9);
      }
    }

    .prev-btn {
      background-color: #fff;
      color: #666;
      border-color: #d9d9d9;

      &:hover {
        color: #004c66;
        border-color: #004c66;
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .step-content {
      max-width: 100%;
      padding: 0 16px;
    }

    .step-panel {
      padding: 24px 16px;
    }
  }

  @media (max-width: 768px) {
    .appreciation-entrust {
      padding: 16px;
    }

    .steps-wrapper {
      padding: 0 8px;
      margin-bottom: 24px;
    }

    .step-content {
      padding: 0 8px;
    }

    .step-panel {
      padding: 16px;
      border-radius: 4px;
    }

    .step-actions {
      flex-direction: column; // 小屏幕下垂直排列
      gap: 12px;
      margin-top: 32px;

      .prev-btn,
      .next-btn,
      .submit-btn {
        width: 100%; // 小屏幕下占满宽度
      }
    }
  }
</style>
