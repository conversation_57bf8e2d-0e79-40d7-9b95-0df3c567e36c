<template>
  <div class="account-overview">
    <!-- 上方三个卡片 -->
    <div class="cards-container">
      <!-- 我的账户 -->
      <div class="account-card">
        <div class="card-header">
          <span class="card-title">我的账户</span>
        </div>
        <div class="card-content">
          <div class="balance-label">可用余额</div>
          <div class="balance-amount">
            <span class="currency-symbol">¥</span>
            <span class="integer-part">{{ getIntegerPart(accountBalance) }}</span>
            <span class="decimal-part">.{{ getDecimalPart(accountBalance) }}</span>
          </div>
          <div class="card-actions">
            <a-button type="default" size="small" @click="handleInventory">流水</a-button>
            <a-button type="default" size="small" @click="handleWithdraw">提现</a-button>
            <a-button type="primary" size="small" @click="handleRecharge">充值</a-button>
          </div>
        </div>
      </div>

      <!-- 我的积分 -->
      <div class="account-card">
        <div class="card-header">
          <span class="card-title">我的积分</span>
        </div>
        <div class="card-content">
          <div class="balance-label">可用积分</div>
          <div class="balance-amount">
            <span class="integer-part">{{ formatNumber(pointsBalance) }}</span>
          </div>
          <div class="card-actions">
            <a-button type="default" size="small" @click="handlePointsDetail">积分明细</a-button>
            <a-button type="primary" size="small" @click="handlePointsExchange">去使用</a-button>
          </div>
        </div>
      </div>

      <!-- 我的银行卡 -->
      <div class="account-card bank-card">
        <div class="card-header">
          <span class="card-title bank-card-title">我的银行卡 · {{ bankCards.length }}张</span>
          <div class="card-actions-header">
            <span class="action-link" @click="handleAddCard">添加</span>
            <!-- <span v-if="!isExpanded" class="action-link" @click="handleManageCards">管理</span> -->
            <span v-if="isExpanded && selectedCards.length > 0" class="action-link delete-btn" @click="handleDeleteSelected">
              删除({{ selectedCards.length }})
            </span>
            <span v-if="isExpanded" class="action-link collapse-btn" @click="handleCollapse">收起</span>
          </div>
        </div>
        <div class="card-content">
          <div class="bank-cards-container" :class="{ expanded: isExpanded }">
            <!-- 收起状态：重叠显示 -->
            <div v-if="!isExpanded" class="cards-stack" :class="{ 'single-card': bankCards.length === 1 }" @click="handleExpand">
              <div v-for="(card, index) in bankCards" :key="card.id" class="bank-card-item stacked" :style="getStackedCardStyle(index)">
                <div class="bank-card-display">
                  <div class="card-header">
                    <div class="bank-info">
                      <div class="bank-name">{{ card.bankName }}</div>
                      <div class="account-type">{{ card.accountType }}</div>
                    </div>
                    <div class="bank-logo">{{ card.bankLogo }}</div>
                  </div>
                  <div class="card-number">{{ card.cardNumber }}</div>
                </div>
              </div>
            </div>

            <!-- 展开状态：列表显示 -->
            <div v-else class="cards-expanded">
              <div class="cards-list">
                <div
                  v-for="card in bankCards"
                  :key="card.id"
                  class="bank-card-item expanded"
                  :class="{ selected: selectedCards.includes(card.id) }"
                  @click="handleCardSelect(card.id)"
                >
                  <div class="bank-card-display" :style="{ background: card.bgColor }">
                    <div class="card-header">
                      <div class="bank-info">
                        <div class="bank-name">{{ card.bankName }}</div>
                        <div class="account-type">{{ card.accountType }}</div>
                      </div>
                      <div class="bank-logo">{{ card.bankLogo }}</div>
                    </div>
                    <div class="card-number">{{ card.cardNumber }}</div>
                    <!-- 选中状态的对号 -->
                    <div v-if="selectedCards.includes(card.id)" class="selected-check">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <circle cx="12" cy="12" r="12" fill="#52c41a" />
                        <path d="M9 12l2 2 4-4" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 下方保证金表格 -->
    <div class="margin-table-container">
      <div class="table-header">
        <span class="table-title">我的保证金</span>
      </div>
      <BasicTable @register="registerTable" @navigation-change="handleNavigationChange">
        <!-- 保证金状态列 -->
        <template #status="{ record }">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>

        <!-- 金额格式化 -->
        <template #amount="{ text }">
          <span v-if="text">{{ formatAmount(text) }}</span>
          <span v-else>-</span>
        </template>
      </BasicTable>
    </div>
  </div>
</template>

<script lang="ts" setup name="AccountOverview">
  import { ref } from 'vue';
  import { Modal } from 'ant-design-vue';
  import { useRouter } from 'vue-router';
  import { BasicTable, useTable } from '/@/components/Table';
  import { BasicColumn } from '/@/components/Table';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { formatToDateTime } from '/@/utils/dateUtil';

  const { createMessage } = useMessage();
  const router = useRouter();

  // 账户数据
  const accountBalance = ref(416546.2);
  const pointsBalance = ref(16546);

  // 银行卡数据
  const bankCards = ref([
    {
      id: 1,
      bankName: '天瑞水泥集团有限公司',
      accountType: '对公账户',
      cardNumber: '6222 1234 1234 1234',
      bankLogo: '中国邮政储蓄银行',
      bgColor: 'linear-gradient(180deg, #004C66 0%, #B3C9D1 100%)',
    },
    {
      id: 2,
      bankName: '中国工商银行',
      accountType: '储蓄卡',
      cardNumber: '6222 5678 5678 5678',
      bankLogo: '中国工商银行',
      bgColor: 'linear-gradient(135deg, #c0392b 0%, #e74c3c 100%)',
    },
    {
      id: 3,
      bankName: '中国建设银行',
      accountType: '储蓄卡',
      cardNumber: '6222 9999 9999 9999',
      bankLogo: '中国建设银行',
      bgColor: 'linear-gradient(135deg, #2980b9 0%, #3498db 100%)',
    },
  ]);

  // 银行卡展开状态
  const isExpanded = ref(false);
  const selectedCards = ref<number[]>([]);

  // 表格列定义
  const columns: BasicColumn[] = [
    {
      title: '保证金编号',
      dataIndex: 'marginId',
      width: 160,
    },
    {
      title: '资产名称',
      dataIndex: 'assetName',
      width: 200,
      ellipsis: true,
    },
    {
      title: '资产编号',
      dataIndex: 'assetNo',
      width: 180,
    },
    {
      title: '保证金类型',
      dataIndex: 'marginType',
      width: 120,
    },
    {
      title: '保证金金额（元）',
      dataIndex: 'amount',
      width: 150,
      slots: { customRender: 'amount' },
    },
    {
      title: '缴费方式',
      dataIndex: 'paymentMethod',
      width: 120,
    },
    {
      title: '缴费时间',
      dataIndex: 'paymentTime',
      width: 160,
      customRender: ({ text }) => (text ? formatToDateTime(text) : '-'),
    },
    {
      title: '保证金状态',
      dataIndex: 'status',
      width: 120,
      slots: { customRender: 'status' },
    },
    {
      title: '操作人',
      dataIndex: 'operator',
      width: 100,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 150,
      ellipsis: true,
    },
  ];

  // 导航栏配置
  const navigationItems = [
    { key: 'all', label: '全部保证金', icon: '' },
    { key: 'paid', label: '保证金缴纳', icon: '' },
    { key: 'frozen', label: '保证金冻结', icon: '' },
    { key: 'returned', label: '保证金退还', icon: '' },
  ];

  const activeNavigationKey = ref<string | number>('all');
  const currentNavParams = ref<any>({});

  // 处理导航切换
  function handleNavigationChange(key: string | number) {
    activeNavigationKey.value = key;

    let searchParams = {};
    switch (key) {
      case 'paid':
        searchParams = { status: 1 }; // 已缴纳
        break;
      case 'frozen':
        searchParams = { status: 2 }; // 已冻结
        break;
      case 'returned':
        searchParams = { status: 3 }; // 已退还
        break;
      default:
        searchParams = {}; // 全部
    }

    currentNavParams.value = searchParams;
    reload();
  }

  // 模拟API调用
  async function queryMarginList(params: any) {
    // 这里应该调用真实的API
    console.log('查询保证金列表:', params);

    // 模拟数据
    const mockData = {
      records: [
        {
          marginId: '123456789',
          assetName: 'XX特钢',
          assetNo: '上海XX废旧物资有限公司',
          marginType: '上海市公司',
          amount: 4645.5,
          paymentMethod: '银行汇款转账',
          paymentTime: '2025-05-02 13:00:59',
          status: 1,
          operator: '张三',
          remark: 'XX项目保证金',
        },
        // 可以添加更多模拟数据
      ],
      total: 1,
    };

    return Promise.resolve(mockData);
  }

  // 表格配置
  const [registerTable, { reload }] = useTable({
    api: queryMarginList,
    columns,
    striped: false,
    useSearchForm: false, // 不显示搜索表单
    showTableSetting: false,
    bordered: false,
    showIndexColumn: false,
    canResize: true,
    showNavigation: true,
    navigationItems,
    activeNavigationKey: activeNavigationKey.value,
    inset: true,
    scroll: { y: 400 }, // 设置表格内容区域的滚动高度
  });

  // 格式化金额
  function formatAmount(amount: number): string {
    return amount.toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  }

  // 格式化数字
  function formatNumber(num: number): string {
    return num.toLocaleString('zh-CN');
  }

  // 获取整数部分
  function getIntegerPart(amount: number): string {
    return Math.floor(amount).toLocaleString('zh-CN');
  }

  // 获取小数部分
  function getDecimalPart(amount: number): string {
    const decimal = (amount % 1).toFixed(2).substring(2);
    return decimal;
  }

  // 获取状态颜色
  function getStatusColor(status: number): string {
    switch (status) {
      case 1:
        return 'green'; // 已缴纳
      case 2:
        return 'orange'; // 已冻结
      case 3:
        return 'blue'; // 已退还
      default:
        return 'default';
    }
  }

  // 获取状态文本
  function getStatusText(status: number): string {
    switch (status) {
      case 1:
        return '已缴纳';
      case 2:
        return '已冻结';
      case 3:
        return '已退还';
      default:
        return '-';
    }
  }

  // 卡片操作方法
  function handleInventory() {
    router.push('/account/inventory');
  }

  function handleWithdraw() {
    createMessage.info('提现功能');
  }

  function handleRecharge() {
    createMessage.info('充值功能');
  }

  function handlePointsDetail() {
    router.push('/account/integral');
  }

  function handlePointsExchange() {
    createMessage.info('积分兑换');
  }

  function handleAddCard() {
    createMessage.info('添加银行卡');
  }

  function handleManageCards() {
    createMessage.info('管理银行卡');
  }

  // 银行卡相关方法
  function handleExpand() {
    isExpanded.value = true;
    selectedCards.value = [];
  }

  function handleCollapse() {
    isExpanded.value = false;
    selectedCards.value = [];
  }

  function getStackedCardStyle(index: number) {
    // 只有第一张卡片正常显示，其他卡片不显示
    if (index === 0) {
      return {
        transform: 'translate(0, 0)',
        zIndex: 2,
        background: bankCards.value[index].bgColor,
      };
    }
    return {
      display: 'none',
    };
  }

  function handleCardSelect(cardId: number) {
    const index = selectedCards.value.indexOf(cardId);
    if (index > -1) {
      selectedCards.value.splice(index, 1);
    } else {
      selectedCards.value.push(cardId);
    }
  }

  function handleDeleteSelected() {
    if (selectedCards.value.length === 0) return;

    Modal.confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedCards.value.length} 张银行卡吗？`,
      onOk: () => {
        bankCards.value = bankCards.value.filter((card) => !selectedCards.value.includes(card.id));
        selectedCards.value = [];
        createMessage.success('删除成功');

        // 如果删除后没有卡片了，自动收起
        if (bankCards.value.length === 0) {
          isExpanded.value = false;
        }
      },
    });
  }
</script>

<style lang="less" scoped>
  .account-overview {
    padding: 20px;
    background-color: #f5f5f5;

    .cards-container {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;

      .account-card {
        width: 519px;
        height: 246px;
        background: #fff;
        border-radius: 10px;
        padding: 15px 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        display: flex;
        flex-direction: column;

        .card-header {
          display: flex;
          justify-content: space-between;

          .card-title {
            width: 100%;
            display: block;
            font-size: 18px;
            font-family: 'PingFang Bold';
            color: #262626;
            margin: 0;
            border-bottom: 1px solid;
            border-image: linear-gradient(to right, #ddd, #fff);
            border-image-slice: 1;
            padding-bottom: 10px;
            margin-bottom: 20px;
          }

          .bank-card-title {
            width: auto;
            display: inline;
            border: none;
            padding-bottom: 0;
          }

          .card-actions-header {
            display: flex;
            gap: 16px;

            .action-link {
              font-size: 14px;
              color: #1890ff;
              cursor: pointer;

              &:hover {
                color: #40a9ff;
              }

              &.collapse-btn {
                color: #666;

                &:hover {
                  color: #333;
                }
              }

              &.delete-btn {
                color: #ff4d4f;

                &:hover {
                  color: #ff7875;
                }
              }
            }
          }
        }

        .card-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          z-index: 100;

          .balance-label {
            font-size: 18px;
            font-family: 'PingFang Bold';
            color: #8c8c8c;
            margin-left: 10px;
          }

          .balance-amount {
            display: flex;
            align-items: baseline;
            color: #262626;
            margin: 10px;
            line-height: 1;
            color: #004c66;

            .currency-symbol {
              font-size: 24px;
              font-family: 'DIN Regular';
              margin-right: 2px;
            }

            .integer-part {
              font-size: 44px;
              font-family: 'DIN Regular';
            }

            .decimal-part {
              font-size: 34px;
              font-family: 'DIN Regular';
            }
          }

          .card-actions {
            display: flex;
            justify-content: end;
            gap: 12px;
            border-top: 1px solid #eee;
            border-image: linear-gradient(to right, #fff, #ddd);
            border-image-slice: 1;
            margin-top: 10px;
            padding-top: 15px;

            .ant-btn {
              width: 88px;
              height: 40px;
              border-radius: 6px;
              font-size: 14px;
            }
          }
        }

        &.bank-card {
          .card-content {
            .bank-cards-container {
              position: relative;
              width: 479px;

              .cards-stack {
                position: relative;
                width: 479px;
                height: 162px;
                cursor: pointer;
                transition: all 0.3s ease;

                // 底部重叠效果（仅在有多张卡时显示）
                &::after {
                  content: '';
                  position: absolute;
                  bottom: -8px;
                  left: 50%;
                  transform: translateX(-50%);
                  width: 459px;
                  height: 12px;
                  background: rgba(0, 0, 0, 0.1);
                  border-radius: 0 0 10px 10px;
                  display: block;
                }

                // 如果只有一张卡，隐藏重叠效果
                &.single-card::after {
                  display: none;
                }

                .bank-card-item.stacked {
                  position: absolute;
                  top: 0;
                  left: 0;
                  width: 479px;
                  height: 162px;
                  transition: all 0.3s ease;
                  border-radius: 10px;

                  .bank-card-display {
                    position: relative;
                    height: 100%;
                    border-radius: 10px;
                    padding: 20px;
                    color: white;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    position: relative;
                    overflow: hidden;

                    .card-header {
                      z-index: 1;

                      .bank-info {
                        .bank-name {
                          font-size: 25px;
                          font-family: 'FZZongYi-M05S';
                          margin-bottom: 10px;
                        }

                        .account-type {
                          font-size: 18px;
                        }
                      }

                      .bank-logo {
                        position: absolute;
                        top: 0;
                        right: 0;
                        font-size: 14px;
                        color: #fff;
                        background: rgba(255, 255, 255, 0.2);
                        padding: 6px 12px;
                        border-radius: 0 10px 0 10px;
                        white-space: nowrap;
                      }
                    }

                    .card-number {
                      font-size: 28px;
                      font-family: 'DIN Bold';
                      letter-spacing: 3px;
                      position: relative;
                      z-index: 1;
                      margin-top: 6px;
                    }
                  }
                }
              }

              .cards-expanded {
                .cards-list {
                  max-height: 800px;
                  overflow-y: auto;

                  .bank-card-item.expanded {
                    margin-bottom: 16px;
                    cursor: pointer;
                    transition: all 0.3s ease;

                    .bank-card-display {
                      width: 479px;
                      height: 162px;
                      border-radius: 10px;
                      padding: 20px;
                      color: white;
                      display: flex;
                      flex-direction: column;
                      justify-content: space-between;
                      position: relative;
                      overflow: hidden;
                      transition: all 0.3s ease;

                      .card-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: flex-start;
                        z-index: 1;

                        .bank-info {
                          .bank-name {
                            font-size: 25px;
                            font-family: 'FZZongYi-M05S';
                            margin-bottom: 10px;
                          }

                          .account-type {
                            font-size: 18px;
                          }
                        }

                        .bank-logo {
                          position: absolute;
                          top: 0;
                          right: 0;
                          font-size: 14px;
                          color: #fff;
                          background: rgba(255, 255, 255, 0.2);
                          padding: 6px 12px;
                          border-radius: 0 10px 0 10px;
                          white-space: nowrap;
                        }
                      }

                      .card-number {
                        font-size: 28px;
                        font-family: 'DIN Bold';
                        letter-spacing: 3px;
                        position: relative;
                        z-index: 1;
                        margin-top: 6px;
                      }

                      .selected-check {
                        position: absolute;
                        bottom: 16px;
                        right: 16px;
                        z-index: 10;
                        animation: checkIn 0.3s ease-out;

                        svg {
                          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
                        }
                      }

                      @keyframes checkIn {
                        0% {
                          transform: scale(0);
                          opacity: 0;
                        }
                        50% {
                          transform: scale(1.2);
                        }
                        100% {
                          transform: scale(1);
                          opacity: 1;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    .margin-table-container {
      background: #fff;
      border-radius: 8px;
      padding: 0;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      height: 480px;
      display: flex;
      flex-direction: column;

      .table-header {
        padding: 15px 20px 0;
        flex-shrink: 0;

        .table-title {
          display: block;
          font-size: 18px;
          font-family: 'PingFang Bold';
          color: #262626;
          border-bottom: 1px solid;
          border-image: linear-gradient(to right, #ddd, #fff);
          border-image-slice: 1;
          padding-bottom: 10px;
          margin-bottom: 12px;
        }
      }
      :deep(.table-content) {
        padding: 0 30px;
        flex: 1;
        overflow: hidden;
      }

      :deep(.jeecg-basic-table) {
        height: 100%;
        display: flex;
        flex-direction: column;

        .jeecg-basic-table-header {
          padding: 16px 24px;
          border-bottom: none;
          flex-shrink: 0;

          .jeecg-basic-table-title {
            font-size: 16px;
            font-family: 'PingFang Bold';
            color: #262626;
          }
        }

        .ant-table-wrapper {
          flex: 1;
          display: flex;
          flex-direction: column;

          .ant-table {
            flex: 1;

            .ant-table-container {
              height: 100%;
              display: flex;
              flex-direction: column;

              .ant-table-header {
                flex-shrink: 0;
              }

              .ant-table-body {
                flex: 1;
                overflow-y: auto;
              }
            }
          }

          .ant-pagination {
            flex-shrink: 0;
            margin-top: 16px;
          }
        }

        // 导航栏样式调整
        .jeecg-basic-table-nav {
          margin-bottom: 0;
          padding: 0 24px;

          // 导航栏下方分割线
          &::after {
            content: '';
            display: block;
            height: 1px;
            background-color: #f0f0f0;
            margin-top: 16px;
          }
        }

        // 当没有搜索表单时，导航栏直接连接表格
        &.no-search-form {
          .jeecg-basic-table-nav {
            border-top: none;
          }
        }
      }
    }

    :deep(.ant-tabs-tab) {
      font-size: 16px;
    }
  }
</style>
