import { defHttp } from '/@/utils/http/axios';

enum Api {
  // 物资类型相关
  getMaterialTree = '/hgy/material/hgyMaterialType/getMaterialTree',

  // 供应需求相关
  saveOrderAndSupplyDemand = '/hgy/supplyDemand/hgySupplyDemand/saveOrderAndSupplyDemand',
  updateSupplyDemand = '/hgy/supplyDemand/hgySupplyDemand/updateSupplyDemand',
  queryById = '/hgy/supplyDemand/hgySupplyDemand/queryOrderSupplyDemandById',
}

/**
 * 获取物资类型树形数据
 * @param params
 */
export const getMaterialTree = (params?) => {
  return defHttp.get({ url: Api.getMaterialTree, params });
};

/**
 * 保存供应需求信息（新增）
 * @param params
 */
export const saveOrderAndSupplyDemand = (params) => {
  return defHttp.post({ url: Api.saveOrderAndSupplyDemand, params });
};

/**
 * 更新供应需求信息（修改）
 * @param params
 */
export const updateSupplyDemand = (params) => {
  return defHttp.put({ url: Api.updateSupplyDemand, params });
};

/**
 * 根据ID查询供应需求详情
 * @param params
 */
export const querySupplyDemandById = (params) => {
  return defHttp.get({ url: Api.queryById, params });
};

/**
 * 分页查询供应需求列表
 * @param params
 */
export const querySupplyDemandList = (params) => {
  return defHttp.get({ url: '/hgy/supplyDemand/hgySupplyDemand/list', params });
};

/**
 * 删除供应需求
 * @param params
 */
export const deleteSupplyDemand = (params) => {
  return defHttp.delete({ url: '/hgy/supplyDemand/hgySupplyDemand/delete', params });
};

/**
 * 根据ID查询物资类型
 * @param params
 */
export const queryMaterialTypeById = (params) => {
  return defHttp.get({ url: '/hgy/material/hgyMaterialType/queryById', params });
};

// 物资类型树节点类型定义
export interface MaterialTypeNode {
  id: string;
  parentId: string;
  name: string;
  code: string;
  level: number;
  leaf: number;
  sort: number;
  status: number;
  delFlag: number;
  children?: MaterialTypeNode[];
}

// 供应需求信息类型定义
export interface SupplyDemandInfo {
  id?: string;
  entrustOrderId?: string;
  tenantId?: number;
  userId?: string;
  type?: string; // 供求方式 4供应, 5求购
  infoTitle?: string; // 信息标题
  highlights?: string; // 供应亮点
  materialType?: string; // 物资类型（兼容旧版本）
  materialTypeOne?: string; // 物资类型第一级
  materialTypeTwo?: string; // 物资类型第二级
  materialTypeThree?: string; // 物资类型第三级
  province?: string; // 省份
  city?: string; // 城市
  district?: string; // 区县
  address?: string; // 详细地址
  materialDesc?: string; // 物资详细描述
  depreciationDegree?: number; // 折旧程度(09-九成新 08-八成新...)
  storageMethod?: number; // 存放方式
  quantity?: number; // 物资数量
  unit?: string; // 物资单位
  price?: number; // 物资价格
  brand?: string; // 物资品牌
  model?: string; // 物资型号
  servicePayType?: string; // 服务付费方式 1买方支付；2卖方支付
  validType?: string; // 信息有效期类型
  validDate?: string; // 信息有效期
  relationUser?: string; // 联系人
  relationPhone?: string; // 联系电话
  viewNum?: number; // 围观数量
  status?: number; // 状态(1-草稿 2-待审核 3-审核通过 4-审核拒绝 5-已发布 6-已成交 7-已撤拍 8 已过期)
  delFlag?: number; // 删除状态
  createBy?: string; // 创建人
  createTime?: string; // 创建时间
  updateBy?: string; // 更新人
  updateTime?: string; // 更新时间
  attachmentList?: AttachmentInfo[]; // 附件信息
}

// 附件信息类型定义
export interface AttachmentInfo {
  id?: string;
  tenantId?: number;
  userId?: string;
  bizType?: string; // 业务类型
  bizId?: string; // 业务ID
  fileName?: string; // 文件名称
  filePath?: string; // 文件路径
  fileSize?: number; // 文件大小(字节)
  fileType?: string; // 文件类型
  createTime?: string; // 上传时间
  delFlag?: number; // 删除状态
  createBy?: string; // 创建人
  updateBy?: string; // 更新人
  updateTime?: string; // 更新时间
}

// 委托单信息类型定义
export interface EntrustOrderInfo {
  id?: string;
  tenantId?: number;
  userId?: string;
  entrustCompanyId?: number; // 委托企业ID
  entrustCompanyName?: string; // 委托企业名称
  onEntrustCompanyId?: number; // 受委托企业ID
  onEntrustCompanyName?: string; // 受委托企业名称
  entrustType?: number; // 委托类型(1-增值 2-自主)
  serviceType?: number; // 服务类型(1-竞价委托 2-资产处置 3-采购信息)
  status?: number; // 状态(1-草稿 2-待审核 3-审核通过 4-审核拒绝 5-已发布 6-已成交 7-已撤拍)
  relationUser?: string; // 联系人
  relationPhone?: string; // 联系电话
  auditOpinion?: string; // 审核意见
  auditTime?: Record<string, unknown>; // 审核时间
  auditUser?: string; // 审核人
  createTime?: Record<string, unknown>; // 创建时间
  updateTime?: Record<string, unknown>; // 更新时间
  delFlag?: number; // 删除状态
  createBy?: string; // 创建人
  updateBy?: string; // 更新人
}

// 保存供应需求请求参数类型
export interface SaveSupplyDemandParams {
  hgyEntrustOrder?: EntrustOrderInfo;
  hgySupplyDemand?: SupplyDemandInfo;
}
