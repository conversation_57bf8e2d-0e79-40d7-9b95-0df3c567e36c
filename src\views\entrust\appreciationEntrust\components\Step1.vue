<template>
  <div class="step-panel">
    <!-- 使用自定义布局，不使用 Ant Design 的默认表单布局 -->
    <a-form ref="formRef" :model="formData" :rules="formRules" :validate-trigger="['change', 'blur']" :scroll-to-first-error="true">
      <!-- 服务类型选择 -->
      <div class="form-section">
        <h3 class="section-title">服务类型</h3>
        <div class="form-row">
          <a-form-item name="serviceType" class="service-type-item">
            <a-select
              v-model:value="formData.serviceType"
              @change="handleServiceTypeChange"
              placeholder="请选择服务类型"
              size="large"
              class="service-type-select"
              :disabled="props.isEditMode"
            >
              <a-select-option :value="1">发布竞价委托</a-select-option>
              <a-select-option :value="2">发布资产处置</a-select-option>
              <a-select-option :value="3">发布采购信息</a-select-option>
            </a-select>
            <div v-if="props.isEditMode" class="edit-mode-tip">
              <span class="tip-text">编辑模式下不允许修改服务类型</span>
            </div>
          </a-form-item>
        </div>
      </div>

      <!-- 委托信息 -->
      <div class="form-section">
        <h3 class="section-title">{{ formData.serviceType === 3 ? '采购信息' : '委托信息' }}</h3>
        <!-- 发布采购信息时只显示公告名称 -->
        <div v-if="formData.serviceType === 3" class="form-row">
          <a-form-item label="公告名称" :name="['entrustInfo', 'noticeName']" required class="form-item-full">
            <a-input v-model:value="formData.entrustInfo.noticeName" placeholder="请输入公告名称" size="large" />
          </a-form-item>
        </div>
        <!-- 发布竞价委托和资产处置时显示委托单位和受委托单位 -->

        <div v-else class="form-row entrust-row">
          <a-form-item label="委托单位" :name="['entrustInfo', 'title']" required class="entrust-item">
            <a-input v-model:value="formData.entrustInfo.title" placeholder="委托单位" size="large" disabled readonly />
          </a-form-item>
          <a-form-item label="受委托单位" :name="['entrustInfo', 'type']" required class="entrust-item">
            <a-input v-model:value="formData.entrustInfo.type" placeholder="受委托单位" size="large" disabled readonly />
          </a-form-item>
          <!-- 占位元素，保持三等分布局 -->
          <div class="entrust-placeholder"></div>
        </div>
      </div>

      <!-- 基本信息 -->
      <div class="form-section" v-if="formData.serviceType !== 3">
        <h3 class="section-title">基本信息</h3>

        <!-- 竞价委托信息 -->
        <div v-if="formData.serviceType === 1">
          <!-- 标的名称独占一行 -->
          <div class="form-row">
            <a-form-item label="标的名称" :name="['basicInfo', 'subjectName']" required class="subject-name-item">
              <a-input v-model:value="formData.basicInfo.subjectName" placeholder="请输入标的名称" size="large" />
            </a-form-item>
          </div>

          <!-- 标的数量、计量单位、拍卖日期三项在一行 -->
          <div class="form-row basic-three-row">
            <a-form-item label="标的数量" :name="['basicInfo', 'subjectQuantity']" required class="basic-three-item">
              <a-input-number
                v-model:value="formData.basicInfo.subjectQuantity"
                placeholder="请输入标的数量"
                style="width: 100%"
                size="large"
                :min="1"
              />
            </a-form-item>
            <a-form-item label="计量单位" :name="['basicInfo', 'measurementUnit']" required class="basic-three-item">
              <!-- <a-select v-model:value="formData.basicInfo.measurementUnit" placeholder="请选择计量单位" size="large">
                <a-select-option value="台">台</a-select-option>
                <a-select-option value="辆">辆</a-select-option>
                <a-select-option value="套">套</a-select-option>
                <a-select-option value="个">个</a-select-option>
                <a-select-option value="件">件</a-select-option>
                <a-select-option value="批">批</a-select-option>
                <a-select-option value="米">米</a-select-option>
              </a-select> -->
              <a-input v-model:value="formData.basicInfo.measurementUnit" placeholder="请输入计量单位" size="large" />
            </a-form-item>
            <a-form-item label="拍卖日期" :name="['basicInfo', 'auctionDate']" required class="basic-three-item">
              <a-date-picker
                v-model:value="formData.basicInfo.auctionDate"
                placeholder="请选择拍卖日期"
                style="width: 100%"
                size="large"
                show-time
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </a-form-item>
          </div>

          <!-- 是否展示实际数量 -->
          <div class="form-row">
            <a-form-item label="是否展示实际数量" :name="['basicInfo', 'quantityFlag']" required class="quantity-flag-item">
              <a-radio-group v-model:value="formData.basicInfo.quantityFlag" size="large">
                <a-radio :value="1">是</a-radio>
                <a-radio :value="0">否</a-radio>
              </a-radio-group>
            </a-form-item>
          </div>

          <!-- 是否设置保留价和保留价输入框在同一行 -->
          <div class="form-row reserve-price-row">
            <!-- 是否设置保留价 -->
            <a-form-item label="是否设置保留价" :name="['basicInfo', 'hasReservePrice']" required class="reserve-price-radio-item">
              <a-radio-group v-model:value="formData.basicInfo.hasReservePrice" size="large">
                <a-radio value="yes">是</a-radio>
                <a-radio value="no">否</a-radio>
              </a-radio-group>
            </a-form-item>

            <!-- 保留价输入框 - 当选择"是"时显示 -->
            <a-form-item
              label="保留价"
              :name="['basicInfo', 'reservePrice']"
              :required="formData.basicInfo.hasReservePrice === 'yes'"
              class="reserve-price-input-item"
              v-show="formData.basicInfo.hasReservePrice === 'yes'"
            >
              <a-input-number
                v-model:value="formData.basicInfo.reservePrice"
                placeholder="请输入保留价"
                style="width: 100%"
                size="large"
                :min="0"
                :precision="2"
                :formatter="(value) => `￥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                :parser="(value) => value.replace(/￥\s?|(,*)/g, '')"
              />
            </a-form-item>
          </div>
        </div>

        <!-- 资产处置信息 -->
        <div v-if="formData.serviceType === 2">
          <!-- 资产名称、资产编号在第一行但不占满屏幕 -->
          <div class="form-row asset-row">
            <a-form-item label="资产名称" :name="['basicInfo', 'assetName']" required class="asset-item">
              <a-input v-model:value="formData.basicInfo.assetName" placeholder="请输入资产名称" size="large" />
            </a-form-item>
            <a-form-item label="资产编号" :name="['basicInfo', 'assetCode']" class="asset-item">
              <a-input v-model:value="formData.basicInfo.assetCode" placeholder="由系统自动生成" size="large" disabled />
            </a-form-item>
            <!-- 占位元素，保持三等分布局 -->
            <div class="asset-placeholder"></div>
          </div>
          <!-- 资产数量、计量单位在第二行同样不占满屏幕 -->
          <div class="form-row asset-row">
            <a-form-item label="资产数量" :name="['basicInfo', 'assetQuantity']" required class="asset-item">
              <a-input-number v-model:value="formData.basicInfo.assetQuantity" placeholder="请输入资产数量" size="large" :min="1" />
            </a-form-item>
            <a-form-item label="计量单位" :name="['basicInfo', 'assetMeasurementUnit']" required class="asset-item">
              <!-- <a-select v-model:value="formData.basicInfo.assetMeasurementUnit" placeholder="请选择计量单位" size="large">
                <a-select-option value="台">台</a-select-option>
                <a-select-option value="辆">辆</a-select-option>
                <a-select-option value="套">套</a-select-option>
                <a-select-option value="个">个</a-select-option>
                <a-select-option value="件">件</a-select-option>
                <a-select-option value="批">批</a-select-option>
              </a-select> -->
              <a-input v-model:value="formData.basicInfo.assetMeasurementUnit" placeholder="请输入计量单位" size="large" />
            </a-form-item>
            <!-- 占位元素，保持三等分布局 -->
            <div class="asset-placeholder"></div>
          </div>

          <!-- 第三行：是否展示实际数量、支付方式 -->
          <div class="form-row asset-row">
            <a-form-item label="是否展示实际数量" :name="['basicInfo', 'assetQuantityFlag']" required class="asset-item">
              <a-radio-group v-model:value="formData.basicInfo.assetQuantityFlag" size="large">
                <a-radio :value="1">是</a-radio>
                <a-radio :value="0">否</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item label="支付方" :name="['basicInfo', 'servicePayType']" required class="asset-item">
              <a-radio-group v-model:value="formData.basicInfo.servicePayType" size="large">
                <a-radio :value="1">买方支付</a-radio>
                <a-radio :value="2">卖方支付</a-radio>
              </a-radio-group>
            </a-form-item>
            <!-- 占位元素，保持三等分布局 -->
            <div class="asset-placeholder"></div>
          </div>
        </div>
      </div>

      <!-- 存放位置/所属地区 -->
      <div class="form-section">
        <h3 class="section-title">{{ formData.serviceType === 3 ? '所属地区' : '存放位置' }}</h3>
        <!-- 发布采购信息时只显示省市区选择 -->
        <div v-if="formData.serviceType === 3" class="form-row">
          <div class="location-selects">
            <a-form-item :name="['location', 'province']" class="location-area-item">
              <JAreaSelect
                v-model:province="formData.location.province"
                v-model:city="formData.location.city"
                v-model:area="formData.location.area"
                placeholder="请选择所属地区"
                :level="3"
              />
            </a-form-item>
          </div>
        </div>
        <!-- 其他服务类型显示省市区选择和详细地址 -->
        <div v-else class="form-row location-row">
          <!-- 省市区级联选择区域（占一半空间） -->
          <div class="location-selects">
            <!-- 使用JeecgBoot内置的省市区选择组件 -->
            <!-- 为了校验，需要分别为省市区设置表单项，但视觉上是一个整体 -->
            <a-form-item :name="['location', 'province']" class="location-area-item">
              <JAreaSelect
                v-model:province="formData.location.province"
                v-model:city="formData.location.city"
                v-model:area="formData.location.area"
                placeholder="请选择存放位置"
                :level="3"
              />
            </a-form-item>
          </div>

          <!-- 详细地址区域（占一半空间） -->
          <div class="detail-address">
            <a-form-item label="详细地址" :name="['location', 'detailAddress']" required class="detail-address-item">
              <a-input v-model:value="formData.location.detailAddress" placeholder="请输入详细地址" size="large">
                <template #suffix>
                  <a-button type="text" @click="getCurrentLocation" :loading="locationLoading" class="location-btn" size="small">
                    <template #icon>
                      <EnvironmentOutlined />
                    </template>
                  </a-button>
                </template>
              </a-input>
            </a-form-item>
          </div>
        </div>
      </div>

      <!-- 资料上传 -->
      <div class="form-section">
        <h3 class="section-title">资料上传</h3>

        <!-- 发布采购信息时只显示采购附件上传 -->
        <div v-if="formData.serviceType === 3">
          <div class="form-row">
            <a-form-item label="采购附件" :name="['materials', 'attachments']" required class="upload-item">
              <div class="upload-container">
                <JUpload
                  v-model:value="formData.materials.attachments"
                  :multiple="true"
                  :max-count="5"
                  accept=".pdf,.doc,.docx,.xls,.xlsx"
                  :return-url="false"
                  class="upload-component upload-normal"
                />
                <div class="upload-tip">支持PDF、DOC、DOCX、XLS、XLSX格式</div>
              </div>
            </a-form-item>
          </div>
        </div>

        <!-- 其他服务类型显示完整的资料上传 -->
        <div v-else>
          <!-- 封面图片上传（仅发布资产处置显示） -->
          <div class="form-row" v-if="formData.serviceType === 2">
            <a-form-item label="封面图片" :name="['materials', 'coverImage']" class="upload-item">
              <div class="upload-container">
                <JUpload
                  v-model:value="formData.materials.coverImage"
                  :multiple="false"
                  :max-count="1"
                  accept="image/*"
                  list-type="picture-card"
                  file-type="image"
                  :return-url="false"
                  class="upload-component upload-normal"
                />
                <div class="upload-tip">建议尺寸800*800 像素，不超过10MB</div>
              </div>
            </a-form-item>
          </div>

          <!-- 图片上传 -->
          <div class="form-row">
            <a-form-item :label="formData.serviceType === 2 ? '资产图片' : '标的图片'" :name="['materials', 'images']" class="upload-item">
              <div class="upload-container">
                <JUpload
                  v-model:value="formData.materials.images"
                  :multiple="true"
                  :max-count="10"
                  accept="image/*"
                  list-type="picture-card"
                  file-type="image"
                  :return-url="false"
                  class="upload-component upload-normal"
                />
                <div class="upload-tip">最多可上传10张图片，支持JPG、PNG格式，单个文件不超过5MB</div>
              </div>
            </a-form-item>
          </div>

          <!-- 附件上传 -->
          <div class="form-row">
            <a-form-item label="附件上传" :name="['materials', 'attachments']" class="upload-item">
              <div class="upload-container">
                <JUpload
                  v-model:value="formData.materials.attachments"
                  :multiple="true"
                  :max-count="5"
                  accept=".pdf,.doc,.docx,.xls,.xlsx"
                  :return-url="false"
                  class="upload-component upload-normal"
                />
                <div class="upload-tip">支持PDF、DOC、DOCX、XLS、XLSX格式</div>
              </div>
            </a-form-item>
          </div>

          <!-- 委托单上传（仅竞价委托显示） -->
          <div class="form-row" v-if="formData.serviceType === 1">
            <a-form-item label="委托单上传" :name="['materials', 'entrustDocument']" required class="upload-item">
              <div class="upload-container">
                <JUpload
                  v-model:value="formData.materials.entrustDocument"
                  :multiple="false"
                  :max-count="1"
                  accept=".pdf,.doc,.docx"
                  :return-url="false"
                  class="upload-component upload-entrust"
                />
                <div class="upload-tip">请上传正式的委托单文件</div>
              </div>
            </a-form-item>
          </div>

          <!-- 特殊说明 -->
          <div class="form-row">
            <a-form-item label="特殊说明" name="materials.specialNote" class="form-item-full">
              <a-textarea v-model:value="formData.materials.specialNote" placeholder="请输入特殊说明（如有）" :rows="3" size="large" />
            </a-form-item>
          </div>
        </div>
      </div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, watch } from 'vue';
  import { EnvironmentOutlined } from '@ant-design/icons-vue';
  import { JUpload, JAreaSelect } from '@/components/Form';
  import type { FormInstance } from 'ant-design-vue';

  // 定义 Props
  interface Props {
    modelValue: {
      serviceType: number;
      entrustInfo: {
        title: string;
        type: string;
        description: string;
        noticeName: string; // 公告名称（仅用于采购信息）
      };
      basicInfo: {
        subjectName: string;
        subjectQuantity: string;
        measurementUnit: string;
        auctionDate: string;
        quantityFlag: number; // 是否展示实际数量 0-否 1-是
        hasReservePrice: string;
        reservePrice?: number; // 保留价字段
        assetName: string;
        assetCode: string;
        assetQuantity: string;
        assetMeasurementUnit: string;
        assetQuantityFlag: number; // 资产处置时的是否展示实际数量 0-否 1-是
        servicePayType: number; // 支付方式 1-买方支付 2-卖方支付
      };
      location: {
        province: string;
        city: string;
        area: string;
        detailAddress: string;
      };
      materials: {
        images: string;
        attachments: string;
        entrustDocument: string;
        specialNote: string;
        coverImage: string;
      };
      // 添加 hgyAttachmentList 字段用于回显
      hgyAttachmentList?: any[];
    };
    locationLoading: boolean;
    isEditMode?: boolean;
  }

  const props = defineProps<Props>();

  // 定义 Emits
  interface Emits {
    (e: 'update:modelValue', value: Props['modelValue']): void;
    (e: 'areaChange', value: string | string[]): void;
    (e: 'getCurrentLocation'): void;
    (e: 'serviceTypeChange', value: number): void;
  }

  const emit = defineEmits<Emits>();

  // 表单引用
  const formRef = ref<FormInstance>();

  // 计算属性：表单数据
  const formData = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
  });

  // 表单校验规则
  const formRules = {
    serviceType: [{ required: true, message: '请选择服务类型', trigger: 'change' }],
    // 委托信息校验规则（委托单位和受委托单位自动填充，无需校验）
    entrustInfo: {
      // title: [{ required: true, message: '请选择委托单位', trigger: 'change' }],
      // type: [{ required: true, message: '请选择受委托单位', trigger: 'change' }],
      noticeName: [{ required: true, message: '请输入公告名称', trigger: 'blur' }], // 公告名称校验规则
    },
    // 基本信息校验规则
    basicInfo: {
      subjectName: [{ required: true, message: '请输入标的名称', trigger: 'blur' }],
      subjectQuantity: [{ required: true, message: '请输入标的数量', trigger: 'blur' }],
      measurementUnit: [{ required: true, message: '请输入计量单位', trigger: 'change' }],
      auctionDate: [{ required: true, message: '请选择拍卖日期', trigger: 'change' }],
      quantityFlag: [{ required: true, message: '请选择是否展示实际数量', trigger: 'change' }],
      hasReservePrice: [{ required: true, message: '请选择是否设置保留价', trigger: 'change' }],
      reservePrice: [
        {
          validator: (rule: any, value: any) => {
            // 只有当选择"是否设置保留价"为"是"时才进行必填校验
            if (formData.value.basicInfo.hasReservePrice === 'yes') {
              if (!value && value !== 0) {
                return Promise.reject('请输入保留价');
              }
              if (value < 0) {
                return Promise.reject('保留价不能为负数');
              }
            }
            return Promise.resolve();
          },
          trigger: 'blur',
        },
      ], // 保留价动态校验规则
      assetName: [{ required: true, message: '请输入资产名称', trigger: 'blur' }],
      assetQuantity: [{ required: true, message: '请输入资产数量', trigger: 'blur' }],
      assetMeasurementUnit: [{ required: true, message: '请输入计量单位', trigger: 'change' }],
      assetQuantityFlag: [{ required: true, message: '请选择是否展示实际数量', trigger: 'change' }],
      servicePayType: [{ required: true, message: '请选择支付方', trigger: 'change' }],
    },
    // 存放位置校验规则
    location: {
      province: [{ required: true, message: '请选择省份', trigger: 'change' }],
      detailAddress: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
    },
    // 资料上传校验规则
    materials: {
      entrustDocument: [{ required: true, message: '请上传委托单文件', trigger: 'change' }],
    },
  };

  // 表单校验方法
  const validateForm = async (): Promise<boolean> => {
    console.log('校验前表单数据:', formData.value);
    try {
      await formRef.value?.validate();
      return true;
    } catch (error) {
      console.error('表单校验失败:', error);
      // 滚动到第一个错误字段
      const firstErrorField = document.querySelector('.ant-form-item-has-error');
      if (firstErrorField) {
        firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
      return false;
    }
  };

  // 暴露校验方法给父组件
  defineExpose({
    validateForm,
  });

  // 处理附件列表回显的函数
  const processAttachmentList = (attachmentList: any[]) => {
    // 分离图片、委托单和附件
    const images: any[] = [];
    const entrustDoc: any[] = [];
    const attachments: any[] = [];

    attachmentList.forEach((item) => {
      const fileData = {
        fileName: item.fileName,
        filePath: item.filePath,
        fileSize: item.fileSize,
        fileType: item.fileType,
      };

      // 根据 fileType 判断是图片还是附件
      if (item.fileType === 'image') {
        images.push(fileData);
      } else if (item.fileType === 'wtd') {
        entrustDoc.push(fileData);
      } else {
        attachments.push(fileData);
      }
    });

    // 更新表单数据 - 增值委托的 materials 字段已经是 string 类型
    if (images.length > 0) {
      const imagesJson = JSON.stringify(images);
      formData.value.materials.images = imagesJson;
      console.log('增值委托设置图片数据:', imagesJson);
    }

    if (entrustDoc.length > 0) {
      const entrustDocJson = JSON.stringify(entrustDoc);
      formData.value.materials.entrustDocument = entrustDocJson;
    }

    if (attachments.length > 0) {
      const attachmentsJson = JSON.stringify(attachments);
      formData.value.materials.attachments = attachmentsJson;
    }
  };

  // 监听 hgyAttachmentList 变化，处理附件回显
  watch(
    () => formData.value.hgyAttachmentList,
    (newAttachmentList) => {
      if (newAttachmentList && Array.isArray(newAttachmentList) && newAttachmentList.length > 0) {
        console.log('增值委托Step1组件接收到附件列表，开始处理回显:', newAttachmentList);
        processAttachmentList(newAttachmentList);
      }
    },
    { immediate: true, deep: true }
  );

  // 监听省市区变化
  watch(
    () => [formData.value.location.province, formData.value.location.city, formData.value.location.area],
    (newVal, oldVal) => {
      // 当省市区任一字段发生变化时，触发父组件的areaChange事件
      if (newVal.some((val, index) => val !== oldVal?.[index])) {
        const areaInfo = [formData.value.location.province, formData.value.location.city, formData.value.location.area];
        emit('areaChange', areaInfo);

        // 手动触发表单校验
        setTimeout(() => {
          formRef.value
            ?.validateFields([
              ['location', 'province'],
              ['location', 'city'],
              ['location', 'area'],
            ])
            .catch(() => {});
        }, 100);
      }
    },
    { deep: true }
  );

  // 获取当前位置
  const getCurrentLocation = () => {
    emit('getCurrentLocation');
  };

  // 服务类型变化处理
  const handleServiceTypeChange = (value: number) => {
    emit('serviceTypeChange', value);
  };

  // 监听是否设置保留价的变化，当选择"否"时清空保留价
  watch(
    () => formData.value.basicInfo.hasReservePrice,
    (newValue) => {
      if (newValue === 'no') {
        // 当选择"否"时，清空保留价字段
        formData.value.basicInfo.reservePrice = undefined;
      }
    }
  );
</script>

<style lang="less" scoped>
  .step-panel {
    background: #fff;
    border-radius: 8px;
  }

  .form-section {
    margin-bottom: 32px;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin-bottom: 16px;
    padding-bottom: 8px;
    display: flex;
    align-items: center;
    &::before {
      content: '';
      display: block;
      width: 4px;
      height: 18px;
      margin-right: 8px;
      background-color: #004c66;
    }
  }

  .upload-component {
    cursor: pointer;
  }

  /* 上传容器布局 - 上传组件和提示文字横向排列 */
  .upload-container {
    display: flex;
    align-items: flex-end; /* 底部对齐 */
    gap: 12px; /* 上传组件和文字间距 */
  }

  /* 上传提示文字样式 */
  .upload-tip {
    font-size: 14px;
    color: #999;
    line-height: 1.4;
    align-self: flex-end; /* 与上传组件底部对齐 */
    flex: 1; /* 占据剩余空间 */
  }

  /* 上传项样式 */
  .upload-item {
    width: 100%;
  }

  /* 自定义上传组件样式 - 普通上传按钮（100px*100px） */
  .upload-normal {
    flex-shrink: 0; /* 不缩小 */

    :deep(.ant-upload-select) {
      /* 设置上传区域尺寸和样式 */
      width: 100px !important;
      height: 100px !important;
      background-color: #f2f2f2 !important; /* 背景色 */
      border: 1px solid #ddd !important; /* 边框色，实线 */
      border-radius: 4px !important;
      position: relative !important;
      overflow: hidden !important;

      /* 清除所有可能的默认样式 */
      &::before,
      &::after {
        display: none !important;
      }

      .ant-upload {
        width: 100% !important;
        height: 100% !important;
        background-color: #f2f2f2 !important; /* 背景色 */
        border: none !important;
        border-radius: 4px !important;
        position: relative !important;
        overflow: hidden !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;

        /* 清除所有可能的默认样式和伪元素 */
        &::before,
        &::after {
          display: none !important;
        }

        /* 隐藏默认的图标和文字 */
        .anticon,
        span,
        .ant-upload-text,
        .ant-upload-hint,
        * {
          display: none !important;
        }

        /* 自定义加号样式 */
        &::after {
          content: '+' !important;
          width: 22px !important;
          height: 21px !important;
          font-size: 18px !important; /* 加号字体大小 */
          color: #ddd !important; /* 加号颜色 */
          font-weight: 300 !important;
          position: absolute !important;
          top: 50% !important;
          left: 50% !important;
          transform: translate(-50%, -50%) !important;
          z-index: 10 !important;
          pointer-events: none !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          background: transparent !important;
          border: none !important;
          line-height: 1 !important;
        }
      }

      /* 悬停效果 */
      &:hover {
        background-color: #e8e8e8 !important; /* 悬停时背景色稍微变深 */
        border-color: #bbb !important; /* 悬停时边框色稍微变深 */

        .ant-upload {
          background-color: #e8e8e8 !important;

          &::after {
            color: #004c66 !important; /* 悬停时加号颜色变为主题色 */
          }
        }
      }
    }
  }

  /* 委托单上传样式 - 委托单上传按钮（178px*100px） */
  .upload-entrust {
    flex-shrink: 0; /* 不缩小 */

    :deep(.ant-upload-select) {
      /* 设置委托单上传区域尺寸和样式 */
      width: 178px !important;
      height: 100px !important;
      background-color: #f2f2f2 !important; /* 背景色 */
      border: 1px solid #ddd !important; /* 边框色，实线 */
      border-radius: 4px !important;
      position: relative !important;
      overflow: hidden !important;

      /* 清除所有可能的默认样式 */
      &::before,
      &::after {
        display: none !important;
      }

      .ant-upload {
        width: 100% !important;
        height: 100% !important;
        background-color: #f2f2f2 !important; /* 背景色 */
        border: none !important;
        border-radius: 4px !important;
        position: relative !important;
        overflow: hidden !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;

        /* 清除所有可能的默认样式和伪元素 */
        &::before,
        &::after {
          display: none !important;
        }

        /* 隐藏默认的图标和文字 */
        .anticon,
        span,
        .ant-upload-text,
        .ant-upload-hint,
        * {
          display: none !important;
        }

        /* 自定义加号样式 */
        &::after {
          content: '+' !important;
          width: 22px !important;
          height: 21px !important;
          font-size: 18px !important; /* 加号字体大小 */
          color: #ddd !important; /* 加号颜色 */
          font-weight: 300 !important;
          position: absolute !important;
          top: 50% !important;
          left: 50% !important;
          transform: translate(-50%, -50%) !important;
          z-index: 10 !important;
          pointer-events: none !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          background: transparent !important;
          border: none !important;
          line-height: 1 !important;
        }
      }

      /* 悬停效果 */
      &:hover {
        background-color: #e8e8e8 !important; /* 悬停时背景色稍微变深 */
        border-color: #bbb !important; /* 悬停时边框色稍微变深 */

        .ant-upload {
          background-color: #e8e8e8 !important;

          &::after {
            color: #004c66 !important; /* 悬停时加号颜色变为主题色 */
          }
        }
      }
    }
  }

  /* 基础表单行布局 */
  .form-row {
    display: flex;
    margin-bottom: 16px;
    align-items: flex-start;
  }

  /* 服务类型选择 - 不占满屏幕 */
  .service-type-item {
    width: auto;

    .service-type-select {
      width: 496px; /* 固定宽度496px */
    }

    /* 服务类型选择框文字颜色 */
    :deep(.ant-select-selection-item) {
      color: #004c66 !important;
    }
  }

  /* 委托信息 - 三等分布局，两个输入框占两份 */
  .entrust-row {
    display: flex;
    gap: 20px; /* 间距 */
  }

  .entrust-item {
    flex: 1; /* 每个输入框占一份 */
    min-width: 0; /* 防止内容溢出 */
  }

  .entrust-placeholder {
    flex: 1; /* 占位元素占一份，保持三等分 */
  }

  /* 基本信息 - 标的名称独占一行 */
  .subject-name-item {
    width: 100%;

    :deep(.ant-input) {
      width: 100%;
    }
  }

  /* 基本信息 - 标的数量、计量单位、拍卖日期三项在一行 */
  .basic-three-row {
    gap: 20px;
  }

  .basic-three-item {
    flex: 1;
    min-width: 200px;
  }

  /* 基本信息 - 是否展示实际数量独占一行 */
  .quantity-flag-item {
    width: auto;
  }

  /* 基本信息 - 是否设置保留价独占一行 */
  .reserve-price-item {
    width: auto;
  }

  /* 保留价行布局 - 是否设置保留价和保留价输入框在同一行 */
  .reserve-price-row {
    display: flex;
    gap: 20px;
    align-items: flex-start;
  }

  /* 是否设置保留价单选框样式 */
  .reserve-price-radio-item {
    width: auto;
    flex-shrink: 0; /* 不允许收缩 */
    min-width: 200px; /* 设置最小宽度确保单选框有足够空间 */
  }

  /* 保留价输入框样式 */
  .reserve-price-input-item {
    flex: 1; /* 占据剩余空间 */
    transition: all 0.3s ease; /* 添加平滑过渡效果 */

    :deep(.ant-input-number) {
      width: 100%;
    }
  }

  /* 资产处置信息 - 不占满屏幕 */
  .asset-row {
    display: flex;
    gap: 20px;
    .asset-placeholder {
      flex: 1;
    }
  }

  .asset-item {
    width: auto;
    flex: 1;

    :deep(.ant-input),
    :deep(.ant-input-number),
    :deep(.ant-select) {
      width: 100%; /* 固定宽度，不占满屏幕 */
    }
  }

  /* 存放位置 - 省市区选择和详细地址在一行内 */
  .location-row {
    gap: 20px;
    align-items: flex-start;
  }

  /* 省市区选择区域（占一半空间） */
  .location-selects {
    flex: 1;
    margin-right: 20px;
  }

  .location-area-item {
    margin-bottom: 0;

    :deep(.area-select) {
      display: flex;
      gap: 10px;

      .ant-select {
        flex: 1;
        height: 40px;
        border-radius: 6px;
        font-size: 14px;

        &:hover {
          border-color: #40a9ff;
        }

        &:focus,
        &.ant-select-focused {
          border-color: #40a9ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        // 设置选择框文字居中
        .ant-select-selector {
          display: flex;
          align-items: center;

          .ant-select-selection-item {
            text-align: center;
            width: 100%;
            display: flex;
            align-items: center;
          }

          .ant-select-selection-placeholder {
            text-align: center;
            width: 100%;
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }

  /* 详细地址区域（占一半空间） */
  .detail-address {
    flex: 1;
  }

  .detail-address-item {
    width: 100%;

    :deep(.ant-input) {
      width: 100%;
    }
  }

  /* 表单项占满一行 */
  .form-item-full {
    width: 100%;
  }

  /* 资料上传 - 每一项都独占一行 */
  .form-section:last-child .form-row {
    width: 100%;

    .ant-form-item {
      width: 100%;
    }
  }

  /* 确保label和输入框之间有10px间距 */
  :deep(.ant-form-item-label) {
    padding-right: 0 !important;
  }

  :deep(.ant-form-item-label > label) {
    margin-right: 0;
  }

  /* 统一设置所有表单项的label宽度和间距 */
  :deep(.ant-form-item) {
    margin-bottom: 16px;
    align-items: flex-start; /* 改为顶部对齐，避免校验错误影响 */

    .ant-form-item-label {
      text-align: left;
      width: auto;
      min-width: 90px;
      padding-right: 0;
      display: flex;
      align-items: center; /* label内容上下居中 */
      height: 40px; /* 固定高度，与输入框高度一致 */

      label {
        color: #666; /* label颜色改为#666 */
        font-size: 16px; /* label字体大小16px */
        font-weight: 400; /* 调整字重 */
        line-height: 1;

        &::after {
          content: '';
          margin: 0;
        }
      }
    }

    .ant-form-item-control {
      flex: 1;
      margin-left: 10px; /* label和输入框间距10px */
    }

    /* 确保输入框容器高度固定 */
    .ant-form-item-control-input {
      min-height: 40px;
    }

    /* 确保所有输入框、选择框、数字输入框保持正确大小 */
    .ant-select .ant-select-selector,
    .ant-picker {
      height: 40px !important;
      line-height: 40px !important;
    }

    .ant-input-number-input {
      height: 38px !important;
    }
  }

  /* 资料上传部分的label不需要上下居中 */
  .form-section:last-child {
    :deep(.ant-form-item) {
      align-items: flex-start !important; /* 资料上传部分label不上下居中 */

      .ant-form-item-label {
        align-items: flex-start !important; /* 资料上传部分label内容不上下居中 */
        height: auto !important; /* 取消固定高度 */
        padding-top: 0; /* 移除顶部内边距 */
      }
    }
  }

  /* 定位按钮样式 */
  .location-btn {
    color: #004c66;
    border: none;
    padding: 0;

    &:hover {
      color: #006699;
    }
  }

  /* 禁用输入框样式 */
  .entrust-item {
    :deep(.ant-input[disabled]) {
      background-color: #f5f5f5 !important; /* 禁用状态背景色 */
      color: #666 !important; /* 禁用状态文字颜色 */
      border-color: #d9d9d9 !important; /* 禁用状态边框颜色 */
      cursor: not-allowed !important; /* 禁用状态鼠标样式 */

      &:hover {
        border-color: #d9d9d9 !important; /* 悬停时保持边框颜色不变 */
      }
    }
  }

  /* 响应式布局 */
  @media (max-width: 768px) {
    .entrust-row,
    .basic-three-row,
    .asset-row,
    .reserve-price-row {
      flex-direction: column;
      gap: 16px;
    }

    .location-row {
      flex-direction: column;
    }

    .location-selects {
      flex-direction: column;
    }

    .entrust-item,
    .asset-item {
      :deep(.ant-input),
      :deep(.ant-input-number),
      :deep(.ant-select) {
        width: 100%;
      }
    }

    /* 移动端保留价样式调整 */
    .reserve-price-radio-item {
      min-width: auto;
      width: 100%;
    }

    .reserve-price-input-item {
      width: 100%;
    }

    // 编辑模式提示样式
    .edit-mode-tip {
      margin-top: 4px;

      .tip-text {
        font-size: 12px;
        color: #999;
        line-height: 1.4;
      }
    }
  }
</style>
