/**
 * 拍卖委托相关类型定义
 */

// 服务类型枚举
export enum ServiceTypeEnum {
  AUCTION = 1, // 发布竞价委托
  ASSET_DISPOSAL = 2, // 发布资产处置
}

// 业务类型枚举 - 附件业务类型
export enum BizTypeEnum {
  WTJJ = 'WTJJ', // 增值委托中发布竞价委托时的图片附件等等
  WTZC = 'WTZC', // 增值委托发布资产处置时的图片附件等等
  WTCG = 'WTCG', // 增值委托发布采购委托时的图片附件等等
  ZZJJ = 'ZZJJ', // 自主委托中发布竞价委托时的图片附件等等
  ZZZC = 'ZZZC', // 自主委托发布资产处置时的图片附件等等
  ZZCG = 'ZZCG', // 自主委托发布采购委托时的图片附件等等
}

// 文件类型枚举
export enum FileTypeEnum {
  IMAGE = 'image', // 图片类型
  VIDEO = 'video', // 视频类型
  MP3 = 'mp3', // 音频类型
  ZIP = 'zip', // 压缩文件类型
  PDF = 'pdf', // pdf类型
  PPT = 'ppt', // ppt类型
  EXCEL = 'excel', // xls、xlsx类型
  WORD = 'word', // doc、docx类型
}

// 拍卖方式枚举
export enum AuctionTypeEnum {
  NORMAL = 1, // 01-正常
  REDUCTION = 2, // 02-减价
  BLIND = 3, // 03-盲拍
  MIXED = 4, // 04-混合式报价
  BLIND_DESIGNATED = 5, // 05-盲拍指定版
}

// 结束方式枚举
export enum EndTypeEnum {
  MANUAL = 1, // 01-手动
  AUTO = 2, // 02-自动
}

// 拍卖形式枚举
export enum AuctionFormEnum {
  SYNC = 1, // 01-同步
  ONLINE = 2, // 02-线上
}

// 标的分类枚举
export enum ItemTypeEnum {
  MATERIAL_EQUIPMENT = 1, // 01-物资/设备
  VEHICLE = 2, // 02-机动车
  REAL_ESTATE = 3, // 03-房产
  LAND = 4, // 04-土地
  OTHER = 5, // 05-其他
}

// 拍卖方式枚举（总价/单价）
export enum AuctionModeEnum {
  TOTAL_PRICE = 1, // 01-总价
  UNIT_PRICE = 2, // 02-单价
}

// 附件信息接口
export interface AttachmentInfo {
  /*附件ID */
  id?: string;
  /*租户ID */
  tenantId?: number;
  /*用户ID */
  userId?: string;
  /*业务类型 */
  bizType?: string;
  /*业务ID */
  bizId?: string;
  /*文件名称 */
  fileName?: string;
  /*文件路径 */
  filePath?: string;
  /*文件大小(字节) */
  fileSize?: number;
  /*文件类型 */
  fileType?: string;
  /*上传时间 */
  createTime?: string;
  /*删除状态 */
  delFlag?: number;
  /*创建人 */
  createBy?: string;
  /*更新人 */
  updateBy?: string;
  /*更新时间 */
  updateTime?: string;
}

// 委托单信息接口
export interface HgyEntrustOrder {
  /*委托单ID（修改时需要传递） */
  id?: string;
  /*委托类型(1-增值 2-自主) */
  entrustType?: number;
  /*服务类型(1-竞价委托 2-资产处置 3-采购信息) */
  serviceType?: number;
  /*状态(1-草稿 2-待审核 3-审核通过 4-审核拒绝 5-已发布 6-已成交 7-已撤拍) */
  status?: number;
  /*委托企业ID */
  entrustCompanyId?: string;
  /*委托企业名称 */
  entrustCompanyName?: string;
  /*受委托企业ID */
  onEntrustCompanyId?: string;
  /*受委托企业名称 */
  onEntrustCompanyName?: string;
  /*联系人 */
  relationUser?: string;
  /*联系电话 */
  relationPhone?: string;
}

// 拍卖标的临时信息接口
export interface HgyAuctionItemTemp {
  /*标的ID（修改时需要传递） */
  id?: string;
  /*委托单ID */
  entrustOrderId?: string;
  /*标的名称 */
  itemName?: string;
  /*标的数量 */
  quantity?: string;
  /*计量单位 */
  unit?: string;
  /*拍卖日期 */
  auctionDate?: string;
  /*是否展示实际数量(0-否 1-是) */
  quantityFlag?: number;
  /*是否设置保留价(0-否 1-是) */
  hasReservePrice?: number;
  /*保留价 */
  reservePrice?: number;
  /*省份 */
  province?: string;
  /*城市 */
  city?: string;
  /*区县 */
  district?: string;
  /*详细地址 */
  address?: string;
  /*特殊说明 */
  specialNotes?: string;
  /*附件列表 */
  attachmentList?: {
    /*业务类型 */
    bizType?: string;
    /*文件名称 */
    fileName?: string;
    /*文件路径 */
    filePath?: string;
    /*文件大小(字节) */
    fileSize?: number;
    /*文件类型 */
    fileType?: string;
  }[];
}

// 拍卖标的项目接口
export interface HgyAuctionItem {
  /*标的ID（修改时需要传递） */
  id?: string;
  /*标的序号 */
  itemNo?: string;
  /*标的分类(01-物资/设备 02-机动车 03-房产 04-土地 05-其他) */
  itemType?: number;
  /*标的名称 */
  itemName?: string;
  /*标的标题 */
  itemTitle?: string;
  /*省份 */
  province?: string;
  /*城市 */
  city?: string;
  /*区县 */
  district?: string;
  /*详细地址 */
  address?: string;
  /*封面图片路径 */
  coverImage?: string;
  /*起拍价 */
  startPrice?: number;
  /*评估价格 */
  appraisalPrice?: number;
  /*是否设置保留价(0-否 1-是) */
  hasReservePrice?: number;
  /*保留价 */
  reservePrice?: number;
  /*保证金 */
  deposit?: number;
  /*加价幅度 */
  bidIncrement?: number;
  /*标的数量 */
  quantity?: string;
  /*计量单位 */
  unit?: string;
  /*是否展示实际数量(0-否 1-是) */
  quantityFlag?: number;
  /*展示佣金 */
  showCommission?: string;
  /*拍卖方式(01-总价 02-单价) */
  auctionMode?: number;
  /*自由竞价时间 */
  freeBidTime?: string;
  /*限时竞价时间 */
  timedBidTime?: string;
  /*标的介绍 */
  description?: string;
  /*附件列表 */
  hgyAttachmentList?: AttachmentInfo[];
}

// 拍卖信息接口（只在自主委托中需要）
export interface HgyAuction {
  /*拍卖ID（修改时需要传递） */
  id?: string;
  /*拍卖会名称 */
  auctionName?: string;
  /*拍卖方式(01-正常 02-减价 03-盲拍 04-混合式报价 05-盲拍指定版) */
  auctionType?: number;
  /*委托企业ID */
  entrustCompanyId?: number;
  /*委托企业名称 */
  entrustCompanyName?: string;
  /*报名截止时间 */
  registerEndTime?: string;
  /*开拍时间 */
  startTime?: string;
  /*结束方式(01-手动 02-自动) */
  endType?: number;
  /*拍卖形式(01-同步 02-线上) */
  auctionForm?: number;
  /*封面图片路径 单张 */
  coverImage?: string;
  /*拍卖公告 */
  auctionNotice?: string;
  /*拍卖须知 */
  auctionNotes?: string;
  /*重要声明 */
  importantNotice?: string;
  /*标的表 */
  hgyAuctionItemList?: HgyAuctionItem[];
  /*附件表 */
  hgyAttachmentList?: {
    /*业务类型 */
    bizType?: string;
    /*文件名称 */
    fileName?: string;
    /*文件路径 */
    filePath?: string;
    /*文件大小(字节) */
    fileSize?: number;
    /*文件类型 */
    fileType?: string;
  }[];
}

// 发布竞价委托请求参数（新版本）
export interface AddAuctionItemTempParams {
  /*委托单信息 */
  hgyEntrustOrder: HgyEntrustOrder;
  /*拍卖标的临时信息 */
  hgyAuctionItemTemp: HgyAuctionItemTemp;
  /*拍卖信息（只在自主委托中需要） */
  hgyAuction?: HgyAuction;
}

// 兼容旧版本的接口定义
export interface LegacyAddAuctionItemTempParams {
  // 委托基本信息
  title: string;
  type: string;
  description: string;

  // 根据服务类型的不同字段
  subjectName?: string;
  subjectQuantity?: string;
  measurementUnit?: string;
  auctionDate?: string;
  hasReservePrice?: string;
  assetName?: string;
  assetCode?: string;
  assetQuantity?: string;
  assetMeasurementUnit?: string;

  // 位置信息
  province: string;
  city: string;
  area: string;
  detailAddress: string;
  latitude: string;
  longitude: string;

  // 资料信息
  images: any[];
  attachments: any[];
  entrustDocument: any[];
  specialNote: string;

  // 联系人信息
  contactName: string;
  contactPhone: string;

  // 服务类型
  serviceType: ServiceTypeEnum;
}

// 拍卖项目信息
export interface AuctionItemInfo {
  id: string;
  title: string;
  type: string;
  description: string;
  status: string;
  createTime: string;
  updateTime: string;
  // 其他字段根据实际需要添加
}

// 分页查询参数
export interface PageParams {
  pageNo: number;
  pageSize: number;
  [key: string]: any;
}

// 分页查询结果
export interface PageResult<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}
