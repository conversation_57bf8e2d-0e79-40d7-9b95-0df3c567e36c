<template>
  <div class="step1">
    <a-form :model="formData" :rules="rules" ref="formRef" :scroll-to-first-error="true">
      <!-- 服务类型板块 -->
      <div class="form-section">
        <h3 class="section-title">服务类型</h3>
        <div class="form-row">
          <a-form-item name="serviceType" class="service-type-item">
            <a-select
              v-model:value="formData.serviceType"
              @change="handleServiceTypeChange"
              placeholder="请选择服务类型"
              size="large"
              class="service-type-select"
              :disabled="props.isEditMode"
            >
              <a-select-option :value="1">发布竞价标的</a-select-option>
              <a-select-option :value="2">发布资产处置</a-select-option>
              <a-select-option :value="3">发布采购信息</a-select-option>
            </a-select>
            <div v-if="props.isEditMode" class="edit-mode-tip">
              <span class="tip-text">编辑模式下不允许修改服务类型</span>
            </div>
          </a-form-item>
        </div>
      </div>

      <!-- 拍卖会基本信息板块（仅发布竞价标的显示） -->
      <div v-if="formData.serviceType === 1" class="form-section">
        <div class="section-title">基本信息</div>
        <div class="section-content">
          <!-- 第一行：拍卖会名称、委托单位、拍卖形式 -->
          <div class="form-row basic-three-row">
            <a-form-item label="拍卖会名称" :name="['auctionInfo', 'auctionName']" required class="basic-three-item">
              <a-input v-model:value="formData.auctionInfo.auctionName" placeholder="请输入拍卖会名称" size="large" />
            </a-form-item>
            <a-form-item label="委托单位" :name="['auctionInfo', 'entrustCompany']" required class="basic-three-item">
              <a-input v-model:value="formData.auctionInfo.entrustCompany" placeholder="委托单位" size="large" disabled readonly />
            </a-form-item>
            <!-- <a-form-item label="拍卖形式" :name="['auctionInfo', 'auctionForm']" required class="basic-three-item">
              <a-radio-group v-model:value="formData.auctionInfo.auctionForm">
                <a-radio :value="1">同步</a-radio>
                <a-radio :value="2">线上</a-radio>
              </a-radio-group>
            </a-form-item> -->
            <a-form-item label="" required class="basic-three-item"> </a-form-item>
          </div>

          <!-- 第二行：报名截止、开拍时间、结束方式 -->
          <div class="form-row basic-three-row">
            <a-form-item label="报名截止" :name="['auctionInfo', 'registerEndTime']" required class="basic-three-item">
              <a-date-picker
                v-model:value="formData.auctionInfo.registerEndTime"
                show-time
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择报名截止时间"
                size="large"
                style="width: 100%"
              />
            </a-form-item>
            <a-form-item label="开拍时间" :name="['auctionInfo', 'startTime']" required class="basic-three-item">
              <a-date-picker
                v-model:value="formData.auctionInfo.startTime"
                show-time
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择开拍时间"
                size="large"
                style="width: 100%"
              />
            </a-form-item>
            <!-- <a-form-item label="结束方式" :name="['auctionInfo', 'endType']" required class="basic-three-item">
              <a-radio-group v-model:value="formData.auctionInfo.endType">
                <a-radio :value="1">手动</a-radio>
                <a-radio :value="2">自动</a-radio>
              </a-radio-group>
            </a-form-item> -->
            <a-form-item label="" required class="basic-three-item"> </a-form-item>
          </div>

          <!-- 第三行：拍卖方式 -->
          <!-- <div class="form-row auction-type-row">
            <a-form-item label="拍卖方式" :name="['auctionInfo', 'auctionType']" required class="auction-type-item">
              <a-radio-group v-model:value="formData.auctionInfo.auctionType">
                <a-radio :value="1">正常</a-radio>
                <a-radio :value="2">减价</a-radio>
                <a-radio :value="3">盲拍</a-radio>
                <a-radio :value="4">混合式报价</a-radio>
                <a-radio :value="5">盲拍指定版</a-radio>
              </a-radio-group>
            </a-form-item>
          </div> -->
        </div>
      </div>

      <!-- 资料上传板块（仅发布竞价标的显示） -->
      <div v-if="formData.serviceType === 1" class="form-section">
        <div class="section-title">资料上传</div>
        <div class="section-content">
          <div class="form-row">
            <a-form-item label="封面图片" :name="['other', 'coverImage']" class="upload-item">
              <div class="upload-container">
                <JUpload
                  v-model:value="formData.other.coverImage"
                  :multiple="false"
                  :max-count="1"
                  accept="image/*"
                  list-type="picture-card"
                  file-type="image"
                  class="upload-component upload-normal"
                />
                <div class="upload-tip">建议尺寸800*800 像素，不超过10MB</div>
              </div>
            </a-form-item>
          </div>
        </div>
      </div>

      <!-- 拍卖公告板块（仅发布竞价标的显示） -->
      <div v-if="formData.serviceType === 1" class="form-section">
        <div class="section-title">拍卖公告</div>
        <div class="section-content">
          <a-form-item label="拍卖公告" :name="['auctionInfo', 'noticeContent']" class="form-item-full">
            <JEditorTiptap
              v-model:value="formData.auctionInfo.noticeContent"
              template-type="auction-notice"
              placeholder="请输入拍卖公告内容"
              :auto-focus="false"
            />
          </a-form-item>
        </div>
      </div>

      <!-- 拍卖须知板块（仅发布竞价标的显示） -->
      <div v-if="formData.serviceType === 1" class="form-section">
        <div class="section-title">拍卖须知</div>
        <div class="section-content">
          <a-form-item label="拍卖须知" :name="['auctionInfo', 'rulesContent']" class="form-item-full">
            <JEditorTiptap
              v-model:value="formData.auctionInfo.rulesContent"
              template-type="auction-rules"
              placeholder="请输入拍卖须知内容"
              :auto-focus="false"
            />
          </a-form-item>
        </div>
      </div>

      <!-- 重要声明板块（仅发布竞价标的显示） -->
      <div v-if="formData.serviceType === 1" class="form-section">
        <div class="section-title">重要声明</div>
        <div class="section-content">
          <a-form-item label="重要声明" :name="['auctionInfo', 'statementContent']" class="form-item-full">
            <JEditorTiptap
              v-model:value="formData.auctionInfo.statementContent"
              template-type="important-notice"
              placeholder="请输入重要声明内容"
              :auto-focus="false"
            />
          </a-form-item>
        </div>
      </div>

      <!-- 委托信息板块（仅采购信息显示） -->
      <div v-if="formData.serviceType === 3" class="form-section">
        <div class="section-title">委托信息</div>
        <div class="section-content">
          <a-form-item label="公告名称" :name="['entrustInfo', 'noticeName']" required class="form-item-full">
            <a-input v-model:value="formData.entrustInfo.noticeName" placeholder="请输入公告名称" size="large" />
          </a-form-item>
        </div>
      </div>

      <!-- 基本信息板块（仅资产处置显示） -->
      <div v-if="formData.serviceType === 2" class="form-section">
        <div class="section-title">基本信息</div>
        <div class="section-content">
          <!-- 第一行：处置单位、资产名称、资产编号 -->
          <div class="form-row asset-row">
            <a-form-item label="处置单位" :name="['entrustInfo', 'title']" required class="asset-item">
              <a-input v-model:value="formData.entrustInfo.title" placeholder="处置单位" size="large" disabled readonly />
            </a-form-item>
            <a-form-item label="资产名称" :name="['basicInfo', 'assetName']" required class="asset-item">
              <a-input v-model:value="formData.basicInfo.assetName" placeholder="请输入资产名称" size="large" />
            </a-form-item>
            <a-form-item label="资产编号" :name="['basicInfo', 'assetNo']" class="asset-item">
              <a-input v-model:value="formData.basicInfo.assetNo" placeholder="资产编号" size="large" disabled readonly />
            </a-form-item>
          </div>

          <!-- 第二行：资产类型、资产数量、计量单位 -->
          <div class="form-row asset-row">
            <a-form-item label="资产类型" :name="['basicInfo', 'assetType']" required class="asset-item">
              <a-cascader
                v-model:value="formData.basicInfo.assetType"
                :options="assetTypeOptions"
                :load-data="loadAssetTypeData"
                placeholder="请选择资产类型"
                size="large"
                show-search
                :filter-option="filterAssetType"
                :field-names="{ label: 'name', value: 'code', children: 'children' }"
                change-on-select
                class="asset-type-cascader"
              />
            </a-form-item>
            <a-form-item label="资产数量" :name="['basicInfo', 'quantity']" required class="asset-item">
              <a-input v-model:value="formData.basicInfo.quantity" placeholder="请输入资产数量" size="large" />
            </a-form-item>
            <a-form-item label="计量单位" :name="['basicInfo', 'unit']" required class="asset-item">
              <!-- <a-select v-model:value="formData.basicInfo.unit" placeholder="请选择计量单位" size="large">
                <a-select-option value="台">台</a-select-option>
                <a-select-option value="辆">辆</a-select-option>
                <a-select-option value="套">套</a-select-option>
                <a-select-option value="个">个</a-select-option>
                <a-select-option value="件">件</a-select-option>
                <a-select-option value="批">批</a-select-option>
                <a-select-option value="米">米</a-select-option>
              </a-select> -->
              <a-input v-model:value="formData.basicInfo.unit" placeholder="请输入计量单位" size="large" />
            </a-form-item>
          </div>
          <div class="form-row asset-row">
            <a-form-item label="是否展示实际数量" :name="['basicInfo', 'quantityFlag']" required class="asset-detail-item">
              <a-radio-group v-model:value="formData.basicInfo.quantityFlag">
                <a-radio :value="1">是</a-radio>
                <a-radio :value="0">否</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item label="服务费支付方" :name="['basicInfo', 'servicePayType']" required class="asset-detail-item">
              <a-radio-group v-model:value="formData.basicInfo.servicePayType" size="large">
                <a-radio :value="1">买方支付</a-radio>
                <a-radio :value="2">卖方支付</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item label="" :name="['basicInfo', 'servicePayType']" required class="asset-detail-item"> </a-form-item>
          </div>
        </div>
      </div>

      <!-- 资产详情板块（仅资产处置显示） -->
      <div v-if="formData.serviceType === 2" class="form-section">
        <div class="section-title">资产详情</div>
        <div class="section-content">
          <!-- 第一行：使用年限、新旧程度、当前状态 -->
          <div class="form-row asset-detail-row">
            <a-form-item label="使用年限" :name="['basicInfo', 'serviceLife']" required class="asset-detail-item">
              <a-input-number
                v-model:value="formData.basicInfo.serviceLife"
                placeholder="请输入使用年限"
                size="large"
                style="width: 100%"
                :min="0"
                addon-after="年"
              />
            </a-form-item>
            <a-form-item label="新旧程度" :name="['basicInfo', 'depreciationDegree']" required class="asset-detail-item">
              <a-select v-model:value="formData.basicInfo.depreciationDegree" placeholder="请选择新旧程度" size="large">
                <a-select-option :value="1">九成新</a-select-option>
                <a-select-option :value="2">八成新</a-select-option>
                <a-select-option :value="3">七成新</a-select-option>
                <a-select-option :value="4">六成新</a-select-option>
                <a-select-option :value="5">五成新</a-select-option>
                <a-select-option :value="6">四成新</a-select-option>
                <a-select-option :value="7">三成新</a-select-option>
                <a-select-option :value="8">二成新</a-select-option>
                <a-select-option :value="9">一成新</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="当前状态" :name="['basicInfo', 'currentStatus']" required class="asset-detail-item">
              <a-radio-group v-model:value="formData.basicInfo.currentStatus">
                <a-radio :value="1">在用</a-radio>
                <a-radio :value="2">闲置</a-radio>
                <a-radio :value="3">报废</a-radio>
              </a-radio-group>
            </a-form-item>
          </div>

          <!-- 第二行：评估价值、处置底价、处置时间 -->
          <div class="form-row asset-detail-row">
            <a-form-item label="评估价值" :name="['basicInfo', 'appraisalValue']" required class="asset-detail-item">
              <a-input-number
                v-model:value="formData.basicInfo.appraisalValue"
                placeholder="请输入评估价值"
                size="large"
                style="width: 100%"
                :min="0"
                :precision="2"
                addon-after="元"
              />
            </a-form-item>
            <a-form-item label="处置底价" :name="['basicInfo', 'disposalPrice']" required class="asset-detail-item">
              <a-input-number
                v-model:value="formData.basicInfo.disposalPrice"
                placeholder="请输入处置底价"
                size="large"
                style="width: 100%"
                :min="0"
                :precision="2"
                addon-after="元"
              />
            </a-form-item>
            <a-form-item label="处置时间" :name="['basicInfo', 'disposalStartTime']" required class="asset-detail-item">
              <a-range-picker
                v-model:value="disposalTimeRange"
                show-time
                format="YYYY-MM-DD HH:mm:ss"
                :placeholder="['开始时间', '结束时间']"
                size="large"
                style="width: 100%"
                @change="handleDisposalTimeChange"
              />
            </a-form-item>
          </div>

          <!-- 第三行：是否展示实际数量、付款方式、是否含税 -->
          <div class="form-row asset-detail-row">
            <a-form-item label="付款方式" :name="['basicInfo', 'paymentMethod']" required class="asset-detail-item">
              <a-radio-group v-model:value="formData.basicInfo.paymentMethod">
                <a-radio :value="1">全款</a-radio>
                <a-radio :value="2">分期</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item label="是否含税" :name="['basicInfo', 'isTaxIncluded']" required class="asset-detail-item">
              <a-radio-group v-model:value="formData.basicInfo.isTaxIncluded">
                <a-radio value="0">否</a-radio>
                <a-radio value="1">是</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item label="税点" :name="['basicInfo', 'taxRate']" :required="formData.basicInfo.isTaxIncluded !== '0'" class="asset-detail-item">
              <a-input-number
                v-model:value="formData.basicInfo.taxRate"
                placeholder="请输入税点"
                size="large"
                style="width: 100%"
                :min="0"
                :max="100"
                :precision="2"
                addon-after="%"
                :disabled="formData.basicInfo.isTaxIncluded === '0'"
              />
            </a-form-item>
          </div>
        </div>
      </div>

      <!-- 存放位置板块（发布竞价标的时不显示） -->
      <div v-if="formData.serviceType !== 1" class="form-section">
        <h3 class="section-title">{{ formData.serviceType === 3 ? '所属地区' : '存放位置' }}</h3>
        <!-- 发布采购信息时只显示省市区选择 -->
        <div v-if="formData.serviceType === 3" class="form-row">
          <div class="location-selects">
            <a-form-item :name="['location', 'province']" class="location-area-item">
              <JAreaSelect
                v-model:province="formData.location.province"
                v-model:city="formData.location.city"
                v-model:area="formData.location.area"
                placeholder="请选择所属地区"
                :level="3"
              />
            </a-form-item>
          </div>
        </div>
        <!-- 其他服务类型显示省市区选择和详细地址 -->
        <div v-else class="form-row location-row">
          <!-- 省市区级联选择区域（占一半空间） -->
          <div class="location-selects">
            <a-form-item :name="['location', 'province']" class="location-area-item">
              <JAreaSelect
                v-model:province="formData.location.province"
                v-model:city="formData.location.city"
                v-model:area="formData.location.area"
                placeholder="请选择存放位置"
                :level="3"
              />
            </a-form-item>
          </div>

          <!-- 详细地址区域（占一半空间） -->
          <div class="detail-address">
            <a-form-item label="详细地址" :name="['location', 'detailAddress']" required class="detail-address-item">
              <a-input v-model:value="formData.location.detailAddress" placeholder="请输入详细地址" size="large">
                <template #suffix>
                  <a-button type="text" @click="getCurrentLocation" :loading="locationLoading" class="location-btn" size="small">
                    <template #icon>
                      <EnvironmentOutlined />
                    </template>
                  </a-button>
                </template>
              </a-input>
            </a-form-item>
          </div>
        </div>
      </div>

      <!-- 资料上传板块 -->
      <div class="form-section" v-if="formData.serviceType !== 1">
        <h3 class="section-title">资料上传</h3>

        <!-- 发布采购信息时只显示采购附件上传 -->
        <div v-if="formData.serviceType === 3">
          <div class="form-row">
            <a-form-item label="采购附件" :name="['materials', 'attachments']" required class="upload-item">
              <div class="upload-container">
                <JUpload
                  v-model:value="formData.materials.attachments"
                  :multiple="true"
                  :max-count="5"
                  accept=".pdf,.doc,.docx,.xls,.xlsx"
                  :return-url="false"
                  class="upload-component upload-normal"
                />
                <div class="upload-tip">支持PDF、DOC、DOCX、XLS、XLSX格式</div>
              </div>
            </a-form-item>
          </div>
        </div>

        <!-- 发布资产处置时显示完整的资料上传 -->
        <div v-else-if="formData.serviceType === 2">
          <!-- 封面图片上传 -->
          <div class="form-row">
            <a-form-item label="封面图片" :name="['other', 'coverImage']" class="upload-item">
              <div class="upload-container">
                <JUpload
                  v-model:value="formData.other.coverImage"
                  :multiple="false"
                  :max-count="1"
                  accept="image/*"
                  list-type="picture-card"
                  file-type="image"
                  class="upload-component upload-normal"
                />
                <div class="upload-tip">建议尺寸800*800 像素，不超过10MB</div>
              </div>
            </a-form-item>
          </div>

          <!-- 资产图片上传 -->
          <div class="form-row">
            <a-form-item label="资产图片" :name="['materials', 'images']" class="upload-item">
              <div class="upload-container">
                <JUpload
                  v-model:value="formData.materials.images"
                  :multiple="true"
                  :max-count="10"
                  accept="image/*"
                  list-type="picture-card"
                  file-type="image"
                  :return-url="false"
                  class="upload-component upload-normal"
                />
                <div class="upload-tip">最多可上传10张图片，支持JPG、PNG格式，单个文件不超过5MB</div>
              </div>
            </a-form-item>
          </div>

          <!-- 附件上传 -->
          <div class="form-row">
            <a-form-item label="附件上传" :name="['materials', 'attachments']" class="upload-item">
              <div class="upload-container">
                <JUpload
                  v-model:value="formData.materials.attachments"
                  :multiple="true"
                  :max-count="5"
                  accept=".pdf,.doc,.docx,.xls,.xlsx"
                  :return-url="false"
                  class="upload-component upload-normal"
                />
                <div class="upload-tip">支持PDF、DOC、DOCX、XLS、XLSX格式</div>
              </div>
            </a-form-item>
          </div>
        </div>
      </div>

      <!-- 特殊说明板块（仅资产处置显示） -->
      <div v-if="formData.serviceType === 2" class="form-section">
        <h3 class="section-title">特殊说明</h3>
        <div class="form-row">
          <a-form-item label="特殊说明" :name="['materials', 'specialNote']" class="upload-item">
            <a-textarea v-model:value="formData.materials.specialNote" placeholder="请输入特殊说明（如有）" :rows="3" size="large" />
          </a-form-item>
        </div>
      </div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, computed, nextTick, toRef, onMounted } from 'vue';
  import { EnvironmentOutlined } from '@ant-design/icons-vue';
  import JUpload from '@/components/Form/src/jeecg/components/JUpload/JUpload.vue';
  import JAreaSelect from '@/components/Form/src/jeecg/components/JAreaSelect.vue';
  import JEditorTiptap from '@/components/Form/src/jeecg/components/JEditorTiptap.vue';
  import { getMaterialTree } from '@/api/supplyAndDemand/SupplyDemand';
  import type { MaterialTypeNode } from '@/api/supplyAndDemand/SupplyDemand';
  import dayjs, { Dayjs } from 'dayjs';
  import { getAllTemplates } from '@/utils/template';

  const templates = getAllTemplates();

  // 表单引用
  const formRef = ref();

  // 资产类型选项
  const assetTypeOptions = ref<MaterialTypeNode[]>([]);

  // Props 定义
  interface Props {
    modelValue: {
      serviceType: number;
      auctionInfo: {
        auctionName: string;
        entrustCompany: string;
        auctionForm: number;
        registerEndTime: string | Dayjs;
        startTime: string | Dayjs;
        endType: number;
        auctionType: number;
        auctionNotice: any[];
        auctionRules: any[];
        importantStatement: any[];
        noticeContent: string;
        rulesContent: string;
        statementContent: string;
      };
      entrustInfo: {
        title: string;
        type: string;
        description: string;
        noticeName: string;
      };
      basicInfo: {
        entrustCompanyId: string;
        assetName: string;
        assetNo: string;
        assetType: string[]; // 级联选择器数组格式，存储每一级的code值
        quantity: string;
        unit: string;
        quantityFlag: number; // 是否展示实际数量 0-否 1-是
        serviceLife: number;
        depreciationDegree: number;
        currentStatus: number;
        appraisalValue: number;
        disposalPrice: number;
        disposalStartTime: string;
        disposalEndTime: string;
        paymentMethod: number;
        isTaxIncluded: string;
        taxRate: number;
        servicePayType: number; // 支付方式 1-买方支付 2-卖方支付
      };
      location: {
        province: string;
        city: string;
        area: string;
        detailAddress: string;
        coordinates: {
          latitude: string;
          longitude: string;
        };
      };
      materials: {
        images: any[] | string;
        attachments: any[] | string;
        specialNote: string;
      };
      other: {
        images?: any[] | string;
        attachments?: any[] | string;
        entrustDocument?: any[];
        specialNote?: string;
        coverImage?: string;
      };
      // 添加 hgyAttachmentList 字段用于回显
      hgyAttachmentList?: any[];
    };
    locationLoading?: boolean;
    isEditMode?: boolean;
  }

  // Emits 定义
  interface Emits {
    (e: 'update:modelValue', value: Props['modelValue']): void;
    (e: 'area-change', value: any): void;
    (e: 'get-current-location'): void;
    (e: 'service-type-change', value: number): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  // 表单数据 - 使用toRef来保持响应式连接
  const formData = toRef(props, 'modelValue');

  // 处置时间范围
  const disposalTimeRange = ref<[Dayjs, Dayjs] | null>(null);

  // 初始化处置时间范围
  const initDisposalTimeRange = () => {
    if (formData.value.basicInfo.disposalStartTime && formData.value.basicInfo.disposalEndTime) {
      disposalTimeRange.value = [dayjs(formData.value.basicInfo.disposalStartTime), dayjs(formData.value.basicInfo.disposalEndTime)];
    } else {
      disposalTimeRange.value = null;
    }
  };

  // 监听处置时间相关字段变化，更新时间范围选择器
  watch(
    () => [formData.value.basicInfo.disposalStartTime, formData.value.basicInfo.disposalEndTime],
    () => {
      initDisposalTimeRange();
    },
    { immediate: true }
  );

  // 监听服务类型变化，用于调试
  watch(
    () => formData.value.serviceType,
    (newVal, oldVal) => {
      console.log('Step1组件中serviceType变化:', { oldVal, newVal });
    }
  );

  // 处理附件列表回显的函数
  const processAttachmentList = (attachmentList: any[]) => {
    // 分离图片和附件
    const images: any[] = [];
    const attachments: any[] = [];

    attachmentList.forEach((item) => {
      const fileData = {
        fileName: item.fileName,
        filePath: item.filePath,
        fileSize: item.fileSize,
        fileType: item.fileType,
      };

      // 根据 fileType 判断是图片还是附件
      if (item.fileType === 'image') {
        images.push(fileData);
      } else {
        attachments.push(fileData);
      }
    });

    // 更新表单数据 - JUpload 组件期望接收 JSON 字符串格式
    if (images.length > 0) {
      const imagesJson = JSON.stringify(images);
      formData.value.materials.images = imagesJson;
      formData.value.other.images = imagesJson;
      console.log('设置图片数据:', imagesJson);
    }

    if (attachments.length > 0) {
      const attachmentsJson = JSON.stringify(attachments);
      formData.value.materials.attachments = attachmentsJson;
      formData.value.other.attachments = attachmentsJson;
      console.log('设置附件数据:', attachmentsJson);
    }

    console.log('回显处理完成:', {
      images: images.length,
      attachments: attachments.length,
    });
  };

  // 监听 hgyAttachmentList 变化，处理附件回显
  watch(
    () => formData.value.hgyAttachmentList,
    (newAttachmentList) => {
      if (newAttachmentList && Array.isArray(newAttachmentList) && newAttachmentList.length > 0) {
        console.log('Step1组件接收到附件列表，开始处理回显:', newAttachmentList);
        processAttachmentList(newAttachmentList);
      }
    },
    { immediate: true, deep: true }
  );

  // 处理处置时间变化
  const handleDisposalTimeChange = (dates: [Dayjs, Dayjs] | null) => {
    if (dates) {
      formData.value.basicInfo.disposalStartTime = dates[0].format('YYYY-MM-DD HH:mm:ss');
      formData.value.basicInfo.disposalEndTime = dates[1].format('YYYY-MM-DD HH:mm:ss');
    } else {
      formData.value.basicInfo.disposalStartTime = '';
      formData.value.basicInfo.disposalEndTime = '';
    }
  };

  // 获取当前位置
  const getCurrentLocation = () => {
    emit('get-current-location');
  };

  // 服务类型变化处理
  const handleServiceTypeChange = (value: number) => {
    // 触发父组件的服务类型变化事件
    emit('service-type-change', value);
  };

  // 表单验证规则
  const rules = computed(() => {
    const baseRules = {
      serviceType: [{ required: true, message: '请选择服务类型', trigger: 'change' }],
      location: {
        province: [{ required: true, message: '请选择省份', trigger: 'change' }],
        city: [{ required: true, message: '请选择城市', trigger: 'change' }],
        area: [{ required: true, message: '请选择区域', trigger: 'change' }],
        detailAddress: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
      },
    };

    // 发布竞价标的的验证规则
    if (formData.value.serviceType === 1) {
      return {
        ...baseRules,
        auctionInfo: {
          auctionName: [{ required: true, message: '请输入拍卖会名称', trigger: 'blur' }],
          entrustCompany: [{ required: true, message: '委托单位不能为空', trigger: 'blur' }],
          auctionForm: [{ required: true, message: '请选择拍卖形式', trigger: 'change' }],
          registerEndTime: [{ required: true, message: '请选择报名截止时间', trigger: 'change' }],
          startTime: [{ required: true, message: '请选择开拍时间', trigger: 'change' }],
          endType: [{ required: true, message: '请选择结束方式', trigger: 'change' }],
          auctionType: [{ required: true, message: '请选择拍卖方式', trigger: 'change' }],
        },
      };
    }

    // 采购信息的验证规则
    if (formData.value.serviceType === 3) {
      return {
        ...baseRules,
        entrustInfo: {
          noticeName: [{ required: true, message: '请输入公告名称', trigger: 'blur' }],
        },
      };
    }

    // 资产处置的验证规则
    if (formData.value.serviceType === 2) {
      return {
        ...baseRules,
        entrustInfo: {
          title: [{ required: true, message: '处置单位不能为空', trigger: 'blur' }],
        },
        basicInfo: {
          assetName: [{ required: true, message: '请输入资产名称', trigger: 'blur' }],
          assetType: [{ required: true, message: '请选择资产类型', trigger: 'change' }],
          quantity: [{ required: true, message: '请输入资产数量', trigger: 'blur' }],
          unit: [{ required: true, message: '请输入计量单位', trigger: 'change' }],
          quantityFlag: [{ required: true, message: '请选择是否展示实际数量', trigger: 'change' }],
          serviceLife: [{ required: true, message: '请输入使用年限', trigger: 'blur' }],
          depreciationDegree: [{ required: true, message: '请选择新旧程度', trigger: 'change' }],
          currentStatus: [{ required: true, message: '请选择当前状态', trigger: 'change' }],
          appraisalValue: [{ required: true, message: '请输入评估价值', trigger: 'blur' }],
          disposalPrice: [{ required: true, message: '请输入处置底价', trigger: 'blur' }],
          disposalStartTime: [{ required: true, message: '请选择处置开始时间', trigger: 'change' }],
          disposalEndTime: [{ required: true, message: '请选择处置结束时间', trigger: 'change' }],
          paymentMethod: [{ required: true, message: '请选择付款方式', trigger: 'change' }],
          isTaxIncluded: [{ required: true, message: '请选择是否含税', trigger: 'change' }],
          servicePayType: [{ required: true, message: '请选择支付方', trigger: 'change' }],
        },
      };
    }

    return baseRules;
  });

  // 获取资产类型树形数据
  const fetchAssetTypeTree = async () => {
    try {
      const result = await getMaterialTree();
      console.log('获取资产类型树形数据:', result);
      if (result && Array.isArray(result)) {
        assetTypeOptions.value = result;
      }
    } catch (error) {
      console.error('获取资产类型失败:', error);
    }
  };

  // 懒加载资产类型数据
  const loadAssetTypeData = async (selectedOptions: any[]) => {
    const targetOption = selectedOptions[selectedOptions.length - 1];
    targetOption.loading = true;

    try {
      // 这里可以根据需要实现懒加载逻辑
      // 目前先使用已加载的数据
      targetOption.loading = false;
    } catch (error) {
      console.error('加载子级资产类型失败:', error);
      targetOption.loading = false;
    }
  };

  // 资产类型搜索过滤
  const filterAssetType = (inputValue: string, path: any[]) => {
    return path.some((option) => option.name.toLowerCase().indexOf(inputValue.toLowerCase()) > -1);
  };

  // 组件挂载时获取资产类型数据
  onMounted(() => {
    fetchAssetTypeTree();
  });

  // 验证表单
  const validateForm = async (): Promise<boolean> => {
    try {
      // 添加延迟确保DOM已经渲染完成
      await new Promise((resolve) => setTimeout(resolve, 100));
      await formRef.value?.validate();
      return true;
    } catch (error) {
      console.error('表单验证失败:', error);
      // 滚动到第一个错误字段
      const firstErrorField = document.querySelector('.ant-form-item-has-error');
      if (firstErrorField) {
        firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }

      return false;
    }
  };

  // 暴露验证方法
  defineExpose({
    validateForm,
  });
</script>

<script lang="ts">
  export default {
    name: 'Step1',
    components: {
      JUpload,
      JAreaSelect,
      JEditorTiptap,
      EnvironmentOutlined,
    },
  };
</script>

<style lang="less" scoped>
  .step1 {
    .form-section {
      margin-bottom: 32px;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
      margin-bottom: 16px;
      padding-bottom: 8px;
      display: flex;
      align-items: center;
      &::before {
        content: '';
        display: block;
        width: 4px;
        height: 18px;
        margin-right: 8px;
        background-color: #004c66;
      }
    }

    .form-row {
      &:last-child {
        border-bottom: none;
      }
    }

    // 服务类型选择样式
    .service-type-item {
      margin-bottom: 0;

      .service-type-select {
        width: 496px; /* 固定宽度496px */
      }
      /* 服务类型选择框文字颜色 */
      :deep(.ant-select-selection-item) {
        color: #004c66 !important;
      }
    }

    // 位置选择样式
    .location-row {
      display: flex;
      gap: 24px;
      align-items: flex-start;

      .location-selects {
        flex: 1;
        min-width: 0;

        .location-area-item {
          margin-bottom: 0;

          :deep(.area-select) {
            .ant-select {
              // 设置选择框文字居中
              .ant-select-selector {
                display: flex;
                align-items: center;

                .ant-select-selection-item {
                  text-align: center;
                  width: 100%;
                  display: flex;
                  align-items: center;
                }

                .ant-select-selection-placeholder {
                  text-align: center;
                  width: 100%;
                  display: flex;
                  align-items: center;
                }
              }
            }
          }
        }
      }
    }

    // 为发布采购信息情况下的省市区选择框添加居中样式
    .location-selects {
      .location-area-item {
        :deep(.area-select) {
          .ant-select {
            // 设置选择框文字居中
            .ant-select-selector {
              display: flex;
              align-items: center;

              .ant-select-selection-item {
                text-align: center;
                width: 100%;
                display: flex;
                align-items: center;
              }

              .ant-select-selection-placeholder {
                text-align: center;
                width: 100%;
                display: flex;
                align-items: center;
              }
            }
          }
        }
      }
    }

    .detail-address {
      flex: 1;
      min-width: 0;

      .detail-address-item {
        margin-bottom: 0;

        .location-btn {
          color: #1890ff;
          border: none;
          background: none;
          padding: 0;
          height: auto;
          line-height: 1;
        }
      }
    }

    // 上传组件样式
    .upload-container {
      display: flex;
      align-items: flex-end;
      gap: 12px;
    }

    .upload-tip {
      font-size: 14px;
      color: #999;
      line-height: 1.4;
      align-self: flex-end;
      flex: 1;
    }

    .upload-item {
      width: 100%;
      margin-bottom: 0;

      .upload-container {
        .upload-tip {
          margin-top: 8px;
          color: #999;
          font-size: 12px;
          line-height: 1.4;
        }
      }
    }

    .upload-normal {
      cursor: pointer;
      flex-shrink: 0;

      :deep(.ant-upload-select) {
        width: 100px !important;
        height: 100px !important;
        background-color: #f2f2f2 !important;
        border: 1px solid #ddd !important;
        border-radius: 4px !important;
        position: relative !important;
        overflow: hidden !important;

        &::before,
        &::after {
          display: none !important;
        }

        .ant-upload {
          width: 100% !important;
          height: 100% !important;
          background-color: #f2f2f2 !important;
          border: none !important;
          border-radius: 4px !important;
          position: relative !important;
          overflow: hidden !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;

          &::before,
          &::after {
            display: none !important;
          }

          .anticon,
          span,
          .ant-upload-text,
          .ant-upload-hint,
          * {
            display: none !important;
          }

          &::after {
            content: '+' !important;
            width: 22px !important;
            height: 21px !important;
            font-size: 18px !important;
            color: #ddd !important;
            font-weight: 300 !important;
            position: absolute !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            z-index: 10 !important;
            pointer-events: none !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            background: transparent !important;
            border: none !important;
            line-height: 1 !important;
          }
        }

        &:hover {
          background-color: #e8e8e8 !important;
          border-color: #bbb !important;

          .ant-upload {
            background-color: #e8e8e8 !important;

            &::after {
              color: #004c66 !important;
            }
          }
        }
      }
    }

    .upload-entrust {
      flex-shrink: 0;

      :deep(.ant-upload-select) {
        width: 178px !important;
        height: 100px !important;
        background-color: #f2f2f2 !important;
        border: 1px solid #ddd !important;
        border-radius: 4px !important;
        position: relative !important;
        overflow: hidden !important;

        &::before,
        &::after {
          display: none !important;
        }

        .ant-upload {
          width: 100% !important;
          height: 100% !important;
          background-color: #f2f2f2 !important;
          border: none !important;
          border-radius: 4px !important;
          position: relative !important;
          overflow: hidden !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;

          &::before,
          &::after {
            display: none !important;
          }

          .anticon,
          span,
          .ant-upload-text,
          .ant-upload-hint,
          * {
            display: none !important;
          }

          &::after {
            content: '+' !important;
            width: 22px !important;
            height: 21px !important;
            font-size: 18px !important;
            color: #ddd !important;
            font-weight: 300 !important;
            position: absolute !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            z-index: 10 !important;
            pointer-events: none !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            background: transparent !important;
            border: none !important;
            line-height: 1 !important;
          }
        }

        &:hover {
          background-color: #e8e8e8 !important;
          border-color: #bbb !important;

          .ant-upload {
            background-color: #e8e8e8 !important;

            &::after {
              color: #004c66 !important;
            }
          }
        }
      }
    }

    // 基础表单行布局
    .basic-three-row {
      display: flex;
      gap: 20px;
      margin-bottom: 16px;
    }

    .basic-three-item {
      flex: 1;
      min-width: 200px;
    }

    // 拍卖方式行布局
    .auction-type-row {
      display: flex;
      margin-bottom: 16px;
    }

    .auction-type-item {
      width: 50%;
    }

    // 资产信息行布局
    .asset-row {
      display: flex;
      gap: 20px;
      margin-bottom: 16px;
    }

    .asset-item {
      flex: 1;
      min-width: 200px;
    }

    // 资产详情行布局
    .asset-detail-row {
      display: flex;
      gap: 20px;
      margin-bottom: 16px;
    }

    .asset-detail-item {
      flex: 1;
      min-width: 200px;
    }

    // 表单项样式调整
    :deep(.ant-form-item) {
      margin-bottom: 16px;
      align-items: flex-start;

      .ant-form-item-label {
        text-align: left;
        width: auto;
        min-width: 90px;
        padding-right: 0;
        display: flex;
        align-items: center;
        justify-content: end;
        height: 40px;

        label {
          color: #666;
          font-size: 16px;
          font-weight: 400;
          line-height: 1;

          &::after {
            content: '';
            margin: 0;
          }
        }
      }

      .ant-form-item-control {
        flex: 1;
        margin-left: 10px;
      }

      .ant-form-item-control-input {
        min-height: 40px;
      }

      .ant-select .ant-select-selector,
      .ant-picker {
        height: 40px !important;
        line-height: 40px !important;
      }

      .ant-input-number-input {
        height: 38px !important;
      }
    }

    // 输入框样式
    :deep(.ant-input),
    :deep(.ant-select-selector),
    :deep(.ant-picker),
    :deep(.ant-input-number),
    :deep(.ant-textarea) {
      border-radius: 6px;
    }

    // JUpload 组件样式调整
    :deep(.upload-normal) {
      .ant-upload-select-picture-card {
        width: 104px;
        height: 104px;
        margin-right: 8px;
        margin-bottom: 8px;
      }
    }

    :deep(.upload-entrust) {
      .ant-upload-list {
        .ant-upload-list-item {
          margin-bottom: 8px;
        }
      }
    }

    // 编辑模式提示样式
    .edit-mode-tip {
      margin-top: 4px;

      .tip-text {
        font-size: 12px;
        color: #999;
        line-height: 1.4;
      }
    }

    // 资产类型级联选择器样式
    .asset-type-cascader {
      width: 100%;

      :deep(.ant-cascader-picker) {
        width: 100%;
      }
    }
  }
</style>
