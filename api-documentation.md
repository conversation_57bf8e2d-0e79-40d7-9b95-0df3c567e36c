# API接口文档

## 📋 接口概览

本文档定义了四个板块所需的后端API接口，这些接口需要在当前项目的后端中实现，供灰谷网项目调用。

## 🔗 基础配置

### 请求基础信息

- **Base URL**: `http://your-api-domain.com`
- **Content-Type**: `application/json`
- **Authorization**: `Bear<PERSON> {token}`

### 通用响应格式

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "result": {}, // 具体数据
  "timestamp": 1640995200000
}
```

---

## 📝 1. 发布供求信息板块 API

### 1.1 获取物资类型树

```http
GET /hgy/material/hgyMaterialType/getMaterialTree
```

**响应示例：**

```json
{
  "success": true,
  "result": [
    {
      "id": "1",
      "name": "电子设备",
      "code": "electronics",
      "children": [
        {
          "id": "11",
          "name": "计算机",
          "code": "computer",
          "children": []
        }
      ]
    }
  ]
}
```

### 1.2 保存供求信息

```http
POST /hgy/supplyDemand/hgySupplyDemand/saveOrderAndSupplyDemand
```

**请求参数：**

```json
{
  "type": "4", // 4-供应, 5-需求
  "infoTitle": "出售二手笔记本电脑",
  "highlights": "九成新，性能良好",
  "materialType": "computer",
  "materialDesc": "联想ThinkPad，i7处理器，16G内存",
  "quantity": 1,
  "unit": "台",
  "price": 3000,
  "brand": "联想",
  "model": "ThinkPad X1",
  "depreciationDegree": 9,
  "province": "110000",
  "city": "110100",
  "district": "110101",
  "address": "中关村大街1号",
  "relationUser": "张三",
  "relationPhone": "13800138000",
  "validType": "1", // 1-长期有效, 2-指定日期
  "validDate": "2024-12-31",
  "servicePayType": "1", // 1-买方支付, 2-卖方支付
  "attachments": ["file1.jpg", "file2.pdf"]
}
```

### 1.3 更新供求信息

```http
PUT /hgy/supplyDemand/hgySupplyDemand/updateSupplyDemand
```

### 1.4 根据ID查询详情

```http
GET /hgy/supplyDemand/hgySupplyDemand/queryOrderSupplyDemandById?id={id}
```

---

## 📋 2. 我的发布板块 API

### 2.1 获取发布列表

```http
GET /hgy/supplyDemand/hgySupplyDemand/list
```

**查询参数：**

- `pageNo`: 页码（默认1）
- `pageSize`: 每页数量（默认10）
- `type`: 类型筛选（4-供应, 5-需求）
- `status`: 状态筛选（1-草稿, 2-已发布, 3-已过期, 4-已下架）
- `keyword`: 搜索关键词
- `startDate`: 开始日期
- `endDate`: 结束日期

**响应示例：**

```json
{
  "success": true,
  "result": {
    "records": [
      {
        "id": "1",
        "type": "4",
        "infoTitle": "出售二手笔记本电脑",
        "materialType": "computer",
        "price": 3000,
        "quantity": 1,
        "unit": "台",
        "status": 2,
        "viewCount": 156,
        "createTime": "2024-01-01 10:00:00",
        "updateTime": "2024-01-01 10:00:00",
        "validDate": "2024-12-31"
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

### 2.2 删除发布

```http
DELETE /hgy/supplyDemand/hgySupplyDemand/delete?id={id}
```

### 2.3 批量删除

```http
POST /hgy/supplyDemand/hgySupplyDemand/deleteBatch
```

**请求参数：**

```json
{
  "ids": ["1", "2", "3"]
}
```

### 2.4 更新状态

```http
PUT /hgy/supplyDemand/hgySupplyDemand/updateStatus
```

**请求参数：**

```json
{
  "id": "1",
  "status": 4 // 新状态
}
```

### 2.5 获取统计数据

```http
GET /hgy/supplyDemand/hgySupplyDemand/statistics
```

**响应示例：**

```json
{
  "success": true,
  "result": {
    "totalCount": 100,
    "draftCount": 5,
    "publishedCount": 80,
    "expiredCount": 10,
    "offlineCount": 5,
    "supplyCount": 60,
    "demandCount": 40
  }
}
```

---

## 👤 3. 我的账户板块 API

### 3.1 账户概览

```http
GET /hgy/account/overview
```

**响应示例：**

```json
{
  "success": true,
  "result": {
    "userId": "user123",
    "username": "张三",
    "avatar": "avatar.jpg",
    "level": 5,
    "balance": 10000.0,
    "frozenAmount": 500.0,
    "integral": 2580,
    "integralLevel": 3,
    "publishCount": 25,
    "tradeCount": 12,
    "creditScore": 95
  }
}
```

### 3.2 积分余额

```http
GET /hgy/account/integral/balance
```

### 3.3 积分明细

```http
GET /hgy/account/integral/records
```

**查询参数：**

- `pageNo`: 页码
- `pageSize`: 每页数量
- `type`: 类型（EARN-获得, SPEND-消费）
- `startDate`: 开始日期
- `endDate`: 结束日期

**响应示例：**

```json
{
  "success": true,
  "result": {
    "records": [
      {
        "id": "1",
        "type": "EARN",
        "amount": 100,
        "description": "发布信息奖励",
        "createTime": "2024-01-01 10:00:00",
        "balance": 2580
      }
    ],
    "total": 50,
    "current": 1
  }
}
```

### 3.4 积分规则

```http
GET /hgy/account/integral/rules
```

### 3.5 库存列表

```http
GET /hgy/account/inventory/list
```

**响应示例：**

```json
{
  "success": true,
  "result": {
    "records": [
      {
        "id": "1",
        "name": "笔记本电脑",
        "category": "电子设备",
        "quantity": 10,
        "unit": "台",
        "price": 5000.0,
        "location": "仓库A",
        "status": "NORMAL",
        "lastUpdateTime": "2024-01-01 10:00:00"
      }
    ]
  }
}
```

### 3.6 库存操作

```http
POST /hgy/account/inventory/add
PUT /hgy/account/inventory/update
DELETE /hgy/account/inventory/delete?id={id}
```

### 3.7 库存统计

```http
GET /hgy/account/inventory/statistics
```

---

## 💬 4. 消息中心板块 API

### 4.1 收件箱列表

```http
GET /hgy/message/inbox/list
```

**查询参数：**

- `pageNo`: 页码
- `pageSize`: 每页数量
- `type`: 消息类型
- `isRead`: 是否已读
- `keyword`: 搜索关键词

**响应示例：**

```json
{
  "success": true,
  "result": {
    "records": [
      {
        "id": "1",
        "type": "PRIVATE",
        "title": "询价消息",
        "content": "您好，请问这个商品还有吗？",
        "sender": "user456",
        "senderName": "李四",
        "receiver": "user123",
        "isRead": false,
        "createTime": "2024-01-01 10:00:00",
        "attachments": []
      }
    ],
    "total": 20
  }
}
```

### 4.2 消息详情

```http
GET /hgy/message/inbox/detail?id={id}
```

### 4.3 标记已读

```http
PUT /hgy/message/inbox/read
```

**请求参数：**

```json
{
  "ids": ["1", "2", "3"]
}
```

### 4.4 删除消息

```http
DELETE /hgy/message/inbox/delete?id={id}
```

### 4.5 发件箱列表

```http
GET /hgy/message/outbox/list
```

### 4.6 发送消息

```http
POST /hgy/message/outbox/send
```

**请求参数：**

```json
{
  "receiver": "user456",
  "title": "询价消息",
  "content": "您好，请问这个商品的详细信息？",
  "attachments": ["file1.jpg"]
}
```

### 4.7 系统消息列表

```http
GET /hgy/message/system/list
```

### 4.8 未读消息数量

```http
GET /hgy/message/unread/count
```

**响应示例：**

```json
{
  "success": true,
  "result": {
    "inbox": 5,
    "system": 2,
    "total": 7
  }
}
```

---

## 🔧 通用接口

### 文件上传

```http
POST /sys/common/upload
```

**请求格式：** `multipart/form-data`

**响应示例：**

```json
{
  "success": true,
  "result": {
    "fileName": "image.jpg",
    "filePath": "https://domain.com/upload/2024/01/01/image.jpg",
    "fileSize": 102400
  }
}
```

### 获取字典数据

```http
GET /sys/dict/getDictItems/{dictCode}
```

### 地区数据

```http
GET /sys/area/list?parentCode={parentCode}
```

---

## 🔒 错误码说明

| 错误码 | 说明           |
| ------ | -------------- |
| 200    | 成功           |
| 400    | 请求参数错误   |
| 401    | 未授权         |
| 403    | 禁止访问       |
| 404    | 资源不存在     |
| 500    | 服务器内部错误 |

## 📝 注意事项

1. **认证授权**：所有接口都需要携带有效的JWT token
2. **参数验证**：后端需要对所有输入参数进行验证
3. **数据权限**：确保用户只能访问自己的数据
4. **接口限流**：对频繁调用的接口进行限流保护
5. **日志记录**：记录关键操作的日志信息
