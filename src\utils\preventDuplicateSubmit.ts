import { ref, Ref } from 'vue';
import { message } from 'ant-design-vue';

/**
 * 防重复提交配置接口
 */
export interface PreventDuplicateConfig {
  /** 防重复提交的时间间隔，单位毫秒，默认2000ms */
  interval?: number;
  /** 是否显示重复提交提示，默认true */
  showMessage?: boolean;
  /** 自定义重复提交提示文本 */
  messageText?: string;
  /** 是否在提交成功后自动重置状态，默认true */
  autoReset?: boolean;
  /** 自动重置的延迟时间，单位毫秒，默认1000ms */
  resetDelay?: number;
}

/**
 * 防重复提交返回值接口
 */
export interface PreventDuplicateReturn {
  /** 是否正在提交中 */
  isSubmitting: Ref<boolean>;
  /** 执行提交操作 */
  executeSubmit: <T = any>(submitFn: () => Promise<T>) => Promise<T | null>;
  /** 手动重置提交状态 */
  resetSubmitState: () => void;
  /** 设置提交状态 */
  setSubmitting: (status: boolean) => void;
}

/**
 * 防重复提交组合式函数
 * @param config 配置选项
 * @returns 防重复提交相关方法和状态
 */
export function usePreventDuplicateSubmit(config: PreventDuplicateConfig = {}): PreventDuplicateReturn {
  const {
    interval = 2000,
    showMessage = true,
    messageText = '请勿重复提交，请稍后再试',
    autoReset = true,
    resetDelay = 1000
  } = config;

  const isSubmitting = ref(false);
  let lastSubmitTime = 0;
  let resetTimer: NodeJS.Timeout | null = null;

  /**
   * 执行提交操作
   * @param submitFn 提交函数
   * @returns Promise<T | null>
   */
  async function executeSubmit<T = any>(submitFn: () => Promise<T>): Promise<T | null> {
    const currentTime = Date.now();

    // 检查是否在提交中
    if (isSubmitting.value) {
      if (showMessage) {
        message.warning(messageText);
      }
      return null;
    }

    // 检查时间间隔
    if (currentTime - lastSubmitTime < interval) {
      if (showMessage) {
        message.warning(messageText);
      }
      return null;
    }

    // 清除之前的重置定时器
    if (resetTimer) {
      clearTimeout(resetTimer);
      resetTimer = null;
    }

    try {
      // 设置提交状态
      isSubmitting.value = true;
      lastSubmitTime = currentTime;

      // 执行提交函数
      const result = await submitFn();

      // 如果配置了自动重置，则延迟重置状态
      if (autoReset) {
        resetTimer = setTimeout(() => {
          isSubmitting.value = false;
          resetTimer = null;
        }, resetDelay);
      }

      return result;
    } catch (error) {
      // 提交失败时立即重置状态
      isSubmitting.value = false;
      throw error;
    }
  }

  /**
   * 手动重置提交状态
   */
  function resetSubmitState(): void {
    if (resetTimer) {
      clearTimeout(resetTimer);
      resetTimer = null;
    }
    isSubmitting.value = false;
    lastSubmitTime = 0;
  }

  /**
   * 设置提交状态
   * @param status 提交状态
   */
  function setSubmitting(status: boolean): void {
    isSubmitting.value = status;
    if (!status) {
      lastSubmitTime = 0;
    }
  }

  return {
    isSubmitting,
    executeSubmit,
    resetSubmitState,
    setSubmitting
  };
}

/**
 * 防重复提交装饰器（用于类方法）
 * @param config 配置选项
 * @returns 方法装饰器
 */
export function preventDuplicateSubmit(config: PreventDuplicateConfig = {}) {
  const {
    interval = 2000,
    showMessage = true,
    messageText = '请勿重复提交，请稍后再试'
  } = config;

  let lastSubmitTime = 0;
  let isSubmitting = false;

  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const currentTime = Date.now();

      // 检查是否在提交中
      if (isSubmitting) {
        if (showMessage) {
          message.warning(messageText);
        }
        return null;
      }

      // 检查时间间隔
      if (currentTime - lastSubmitTime < interval) {
        if (showMessage) {
          message.warning(messageText);
        }
        return null;
      }

      try {
        isSubmitting = true;
        lastSubmitTime = currentTime;

        const result = await originalMethod.apply(this, args);

        // 延迟重置状态
        setTimeout(() => {
          isSubmitting = false;
        }, 1000);

        return result;
      } catch (error) {
        isSubmitting = false;
        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * 简单的防重复提交函数（用于单个函数）
 * @param fn 要执行的函数
 * @param config 配置选项
 * @returns 包装后的函数
 */
export function createPreventDuplicateSubmit<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  config: PreventDuplicateConfig = {}
): T {
  const {
    interval = 2000,
    showMessage = true,
    messageText = '请勿重复提交，请稍后再试'
  } = config;

  let lastSubmitTime = 0;
  let isSubmitting = false;

  return (async (...args: any[]) => {
    const currentTime = Date.now();

    // 检查是否在提交中
    if (isSubmitting) {
      if (showMessage) {
        message.warning(messageText);
      }
      return null;
    }

    // 检查时间间隔
    if (currentTime - lastSubmitTime < interval) {
      if (showMessage) {
        message.warning(messageText);
      }
      return null;
    }

    try {
      isSubmitting = true;
      lastSubmitTime = currentTime;

      const result = await fn(...args);

      // 延迟重置状态
      setTimeout(() => {
        isSubmitting = false;
      }, 1000);

      return result;
    } catch (error) {
      isSubmitting = false;
      throw error;
    }
  }) as T;
}

export default usePreventDuplicateSubmit;
