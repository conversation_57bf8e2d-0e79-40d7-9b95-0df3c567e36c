<template>
  <div class="appreciation-publish">
    <!-- 步骤条 -->
    <div class="steps-wrapper">
      <Steps :current="currentStep" :steps="stepsList" @change="handleStepChange" />
    </div>

    <!-- 步骤内容 -->
    <div class="step-content">
      <!-- 第一步：编辑委托信息 -->
      <div v-if="currentStep === 0">
        <Step1
          ref="step1Ref"
          v-model="stepOneData"
          :location-loading="locationLoading"
          :is-edit-mode="isEditMode"
          @area-change="handleAreaChange"
          @get-current-location="getCurrentLocation"
          @service-type-change="handleServiceTypeChange"
        />

        <!-- 操作按钮 -->
        <div class="step-actions">
          <a-button type="primary" size="large" class="next-btn" @click="nextStep"> 下一步 </a-button>
        </div>
      </div>

      <!-- 第二步：联系信息 -->
      <div v-if="currentStep === 1">
        <Step2 ref="step2Ref" v-model="stepTwoData" />

        <!-- 操作按钮 -->
        <div class="step-actions">
          <!-- 修改模式下只显示保存修改按钮 -->
          <template v-if="isEditMode">
            <a-button type="primary" size="large" class="submit-btn" :loading="submitting" @click="submitForm(3)"> 保存修改 </a-button>
          </template>
          <!-- 新增模式下显示原有按钮 -->
          <template v-else>
            <!-- <a-button size="large" class="draft-btn" @click="submitForm(1)"> 保存至草稿 </a-button> -->
            <a-button type="primary" size="large" class="submit-btn" :loading="submitting" @click="submitForm(3)"> 确认发布 </a-button>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref, computed, onMounted, watch, provide } from 'vue';
  import { message } from 'ant-design-vue';
  import { useRouter, useRoute } from 'vue-router';
  import Steps from '@/components/Steps/index.vue';
  import Step1 from './components/Step1.vue';
  import Step2 from './components/Step2.vue';
  // 引入API接口
  import {
    queryPassedReview,
    platformUpdate,
    queryEntrustById,
    queryProcurementById,
    platformUpdateProcurement,
  } from '@/api/manageCenter/appreciationPublish';
  import type { PassedReviewItem, PlatformUpdateParams } from '@/api/manageCenter/appreciationPublish';

  // 组件引用
  const step1Ref = ref<InstanceType<typeof Step1>>();
  const step2Ref = ref<InstanceType<typeof Step2>>();

  // 路由实例
  const router = useRouter();
  const route = useRoute();

  // 当前步骤
  const currentStep = ref(0);

  // 服务类型 (2-发布资产处置 3-发布采购信息)
  const serviceType = ref(2);

  // 提交状态
  const submitting = ref(false);

  // 定位加载状态
  const locationLoading = ref(false);

  // 编辑状态
  const isEditMode = ref(false);
  const editId = ref<string>('');

  // 委托单列表
  const entrustOrderList = ref<PassedReviewItem[]>([]);
  // 委托单加载状态
  const entrustOrderLoading = ref(false);
  // 委托详情加载状态
  const entrustDetailLoading = ref(false);

  // 提供委托单列表数据给子组件
  provide('entrustOrderList', entrustOrderList);
  provide('entrustOrderLoading', entrustOrderLoading);
  provide('entrustDetailLoading', entrustDetailLoading);

  // 动态步骤列表
  const stepsList = computed(() => {
    return [
      {
        title: '编辑委托信息',
        description: '填写委托的基本信息',
      },
      {
        title: '发布委托信息',
        description: '填写联系人信息并发布',
      },
    ];
  });

  // 第一步表单数据
  let stepOneData = reactive({
    // 服务类型
    serviceType: 2,
    // 关联委托单号
    entrustOrderId: '',
    // 委托信息
    entrustInfo: {
      title: '', // 委托单位（显示企业名称）
      type: '', // 受委托单位（显示企业名称）
      description: '', // 委托描述
      noticeName: '', // 公告名称（仅用于采购信息）
      onEntrustCompanyId: '', // 委托企业ID（用于采购信息）
      entrustCompanyId: '', // 委托单位ID（用于采购信息）
    },
    // 基本信息
    basicInfo: {
      // 发布资产处置
      entrustCompanyId: '', // 处置单位ID
      onEntrustCompanyId: '', // 委托企业ID
      assetName: '', // 资产名称
      assetNo: '', // 资产编号
      assetType: [] as string[], // 资产类型（级联选择器数组格式）
      quantity: '', // 资产数量
      unit: '', // 计量单位
      quantityFlag: 0, // 是否展示实际数量 0-否 1-是
      serviceLife: 0, // 使用年限
      depreciationDegree: 1, // 新旧程度(1-九成新 2-八成新...)
      currentStatus: 1, // 当前状态(1-在用 2-闲置 3-报废)
      appraisalValue: 0, // 评估价值
      disposalPrice: 0, // 处置底价
      disposalStartTime: '', // 处置开始时间
      disposalEndTime: '', // 处置结束时间
      paymentMethod: 1, // 付款方式(1-全款 2-分期)
      isTaxIncluded: '0', // 是否含税(0:表示不含税,other：表示含税率)
      taxRate: 0, // 税点
      servicePayType: 1, // 支付方式 1-买方支付 2-卖方支付，默认卖方支付
    },
    // 存放位置
    location: {
      province: '110000', // 省份
      city: '110100', // 城市
      area: '110101', // 区域
      detailAddress: '', // 详细地址
      coordinates: {
        // 坐标信息
        latitude: '',
        longitude: '',
      },
    },
    // 资料上传
    materials: {
      images: [] as any[] | string, // 资产图片
      attachments: [] as any[] | string, // 附件上传
      specialNote: '', // 特殊说明
    },
    other: {
      // 直接字段（与Step1组件Props接口匹配）
      images: [] as any[] | string, // 资产图片
      attachments: [] as any[] | string, // 附件上传
      entrustDocument: [], // 委托单上传
      specialNote: '', // 特殊说明
      coverImage: '', // 封面图片
    },
    // 附件列表（用于回显）
    hgyAttachmentList: [] as any[],
  });

  // 第二步表单数据（联系人信息）
  let stepTwoData = reactive({
    contact: {
      contactName: '', // 联系人姓名
      contactPhone: '', // 联系电话
    },
  });

  watch(
    stepOneData,
    (newVal) => {
      console.log('stepOneData变化:', newVal);
    },
    { deep: true }
  );

  // 处理步骤变化
  const handleStepChange = (step: number) => {
    // 只允许点击当前步骤之前的步骤返回，当前步骤之后的步骤不能通过点击步骤条跳转
    if (step < currentStep.value) {
      currentStep.value = step;
    }
  };

  // 省市区变化处理
  const handleAreaChange = (value: any) => {
    console.log('省市区变化:', value);
  };

  // 获取当前位置
  const getCurrentLocation = () => {
    locationLoading.value = true;

    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          // 模拟根据经纬度获取地址信息
          setTimeout(() => {
            stepOneData.location.detailAddress = '模拟获取的详细地址：北京市海淀区中关村大街1号';
            locationLoading.value = false;
            message.success('位置获取成功');
          }, 1000);
        },
        (error) => {
          locationLoading.value = false;
          message.error('位置获取失败，请手动输入地址');
        }
      );
    } else {
      locationLoading.value = false;
      message.error('浏览器不支持地理位置获取');
    }
  };

  // 服务类型变化处理（现在主要用于内部同步，不再支持手动切换）
  const handleServiceTypeChange = (value: number) => {
    serviceType.value = value;
    // 更新stepOneData中的服务类型，确保数据同步
    stepOneData.serviceType = value;
    console.log('服务类型已更新为:', value === 2 ? '发布资产处置' : '发布采购信息');
  };

  // 获取已通过审核的委托单列表
  const fetchEntrustOrderList = async () => {
    try {
      entrustOrderLoading.value = true;
      const result = await queryPassedReview();
      console.log('获取委托单列表:', result);

      if (result && Array.isArray(result)) {
        entrustOrderList.value = result;

        // 如果不是编辑模式且有委托单列表，默认选择第一个
        if (!isEditMode.value && result.length > 0) {
          stepOneData.entrustOrderId = result[0].entrustOrderNo;
          // 注意：这里会触发 watch 监听，自动获取详情并设置服务类型
        }
      }
    } catch (error) {
      console.error('获取委托单列表失败:', error);
    } finally {
      entrustOrderLoading.value = false;
    }
  };

  // 根据委托单号获取详情数据
  const fetchEntrustDetail = async (entrustOrderId: string) => {
    if (!entrustOrderId) return;

    entrustDetailLoading.value = true;
    try {
      console.log('获取委托详情，委托单号:', entrustOrderId);

      // 根据委托单号前四位判断服务类型和调用的接口
      const prefix = entrustOrderId.substring(0, 4).toUpperCase();
      let serviceTypeValue = 2; // 默认为资产处置

      if (prefix === 'WTCG') {
        serviceTypeValue = 3; // 采购信息
      } else if (prefix === 'WTCZ') {
        serviceTypeValue = 2; // 资产处置
      }

      // 自动设置服务类型
      stepOneData.serviceType = serviceTypeValue;
      serviceType.value = serviceTypeValue;

      let result: any;
      if (serviceTypeValue === 2) {
        // 发布资产处置 - WTCZ前缀
        result = await queryEntrustById({ id: entrustOrderId });

        if (result?.hgyAssetEntrust) {
          const data = result.hgyAssetEntrust;
          // 回显基本信息
          stepOneData.basicInfo = {
            ...stepOneData.basicInfo,
            assetName: data.assetName || '',
            assetNo: data.assetNo || '',
            // 处理资产类型回显，如果是字符串则转换为数组
            assetType: data.assetType ? (typeof data.assetType === 'string' ? data.assetType.split(',') : data.assetType) : '',
            quantity: data.quantity || '',
            unit: data.unit || '台',
            quantityFlag: data.quantityFlag || 1,
            serviceLife: data.serviceLife || 0,
            depreciationDegree: data.depreciationDegree || 1,
            currentStatus: data.currentStatus || 1,
            appraisalValue: data.appraisalValue || 0,
            disposalPrice: data.disposalPrice || 0,
            disposalStartTime: data.disposalStartTime || '',
            disposalEndTime: data.disposalEndTime || '',
            paymentMethod: data.paymentMethod || 1,
            isTaxIncluded: data.isTaxIncluded || '0',
            taxRate: data.taxRate || 0,
            servicePayType: data.servicePayType || 1, // 支付方式，默认买方支付
          };

          // 回显委托信息
          stepOneData.entrustInfo = {
            ...stepOneData.entrustInfo,
            title: result.hgyEntrustOrder?.onEntrustCompanyName || '', // 处置单位名称（受委托单位）
            type: result.hgyEntrustOrder?.entrustCompanyName || '', // 委托单位名称
          };

          // 回显地址信息
          stepOneData.location = {
            ...stepOneData.location,
            province: data.provinceCode || '',
            city: data.cityCode || '',
            area: data.districtCode || '',
            detailAddress: data.address || '',
          };

          // 回显封面图片
          stepOneData.other.coverImage = data.coverImage || '';
        }

        // 回显联系人信息和委托企业信息（资产处置情况下）
        if (result?.hgyEntrustOrder) {
          // 联系人信息
          stepTwoData.contact.contactName = result.hgyEntrustOrder.relationUser || '';
          stepTwoData.contact.contactPhone = result.hgyEntrustOrder.relationPhone || '';

          // 委托企业ID和委托单位ID
          stepOneData.basicInfo.onEntrustCompanyId = result.hgyEntrustOrder.onEntrustCompanyId || '';
          stepOneData.basicInfo.entrustCompanyId = result.hgyEntrustOrder.entrustCompanyId || '';

          console.log('资产处置联系人和委托企业信息回显:', {
            contactName: result.hgyEntrustOrder.relationUser,
            contactPhone: result.hgyEntrustOrder.relationPhone,
            entrustCompanyId: result.hgyEntrustOrder.entrustCompanyId, // 委托单位ID
            onEntrustCompanyId: result.hgyEntrustOrder.onEntrustCompanyId, // 委托企业ID
            entrustCompanyName: result.hgyEntrustOrder.entrustCompanyName, // 委托单位名称
            onEntrustCompanyName: result.hgyEntrustOrder.onEntrustCompanyName, // 处置单位名称（受委托单位）
          });
        }

        // 回显附件信息（资产处置情况下）
        if (result?.hgyAttachmentList && Array.isArray(result.hgyAttachmentList) && result.hgyAttachmentList.length > 0) {
          console.log('资产处置开始处理附件回显:', result.hgyAttachmentList);
          stepOneData.hgyAttachmentList = result.hgyAttachmentList;
        }
      } else if (serviceTypeValue === 3) {
        // 发布采购信息 - WTCG前缀，调用新接口
        result = await queryProcurementById({ id: entrustOrderId });

        if (result?.hgyProcurement) {
          const data = result.hgyProcurement;
          // 回显采购信息
          stepOneData.entrustInfo = {
            ...stepOneData.entrustInfo,
            noticeName: data.noticeName || '',
            title: result.hgyEntrustOrder?.onEntrustCompanyName || '', // 处置单位名称（受委托单位）
            type: result.hgyEntrustOrder?.entrustCompanyName || '', // 委托单位名称
            onEntrustCompanyId: result.hgyEntrustOrder?.onEntrustCompanyId || '', // 委托企业ID
            entrustCompanyId: result.hgyEntrustOrder?.entrustCompanyId || '', // 委托单位ID
          };

          // 回显地址信息
          stepOneData.location = {
            ...stepOneData.location,
            province: data.provinceCode || '',
            city: data.cityCode || '',
            area: data.districtCode || '',
            detailAddress: data.address || '',
          };
        }

        // 回显联系人信息（采购信息情况下）
        if (result?.hgyEntrustOrder) {
          stepTwoData.contact.contactName = result.hgyEntrustOrder.relationUser || '';
          stepTwoData.contact.contactPhone = result.hgyEntrustOrder.relationPhone || '';
          console.log('采购信息联系人和委托企业信息回显:', {
            contactName: result.hgyEntrustOrder.relationUser,
            contactPhone: result.hgyEntrustOrder.relationPhone,
            entrustCompanyId: result.hgyEntrustOrder.entrustCompanyId, // 委托单位ID
            onEntrustCompanyId: result.hgyEntrustOrder.onEntrustCompanyId, // 委托企业ID
            entrustCompanyName: result.hgyEntrustOrder.entrustCompanyName, // 委托单位名称
            onEntrustCompanyName: result.hgyEntrustOrder.onEntrustCompanyName, // 处置单位名称（受委托单位）
          });
        }

        // 回显附件信息（采购信息情况下）
        if (result?.hgyAttachmentList && Array.isArray(result.hgyAttachmentList) && result.hgyAttachmentList.length > 0) {
          console.log('采购信息开始处理附件回显:', result.hgyAttachmentList);
          stepOneData.hgyAttachmentList = result.hgyAttachmentList;
        }
      }
    } catch (error) {
      console.error('获取委托详情失败:', error);
    } finally {
      entrustDetailLoading.value = false;
    }
  };

  // 监听委托单号变化
  watch(
    () => stepOneData.entrustOrderId,
    (newEntrustOrderId) => {
      if (newEntrustOrderId && !isEditMode.value) {
        // 清空之前的数据，避免数据混乱
        stepOneData.entrustInfo = {
          title: '',
          type: '',
          description: '',
          noticeName: '',
          onEntrustCompanyId: '',
          entrustCompanyId: '',
        };

        // 清空联系人信息
        stepTwoData.contact = {
          contactName: '',
          contactPhone: '',
        };

        // 根据委托单号获取详情并自动设置服务类型
        fetchEntrustDetail(newEntrustOrderId);
      }
    }
  );

  // 组件挂载时获取委托单列表和处理编辑模式
  onMounted(() => {
    fetchEntrustOrderList();

    // 检查是否为编辑模式
    const id = route.query.id as string;
    const serviceTypeParam = route.query.serviceType as string;

    if (id) {
      isEditMode.value = true;
      editId.value = id;

      // 设置服务类型
      if (serviceTypeParam) {
        const serviceTypeValue = parseInt(serviceTypeParam);
        serviceType.value = serviceTypeValue;
        stepOneData.serviceType = serviceTypeValue;
      }

      // 如果是编辑模式，获取详情数据
      // fetchEntrustDetail(id);
    }
  });

  // 下一步
  const nextStep = async () => {
    // 验证当前步骤的表单数据
    if (currentStep.value === 0) {
      // 第一步：验证基本信息
      const isValid = await step1Ref.value?.validateForm();
      if (!isValid) {
        return;
      }
    }
    console.log('验证通过，可以跳转到下一步', stepOneData);

    if (currentStep.value < stepsList.value.length - 1) {
      currentStep.value++;
    }
  };

  // 根据文件扩展名判断文件类型的函数
  const getFileTypeByExtension = (filePath: string): string => {
    if (!filePath) return 'other';

    const extension = filePath.toLowerCase().split('.').pop();

    switch (extension) {
      case 'pdf':
        return 'pdf';
      case 'ppt':
      case 'pptx':
        return 'ppt';
      case 'xls':
      case 'xlsx':
        return 'excel';
      case 'doc':
      case 'docx':
        return 'doc';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return 'image';
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
      case 'flv':
        return 'video';
      case 'mp3':
      case 'wav':
      case 'flac':
      case 'aac':
        return 'mp3';
      case 'zip':
      case 'rar':
      case '7z':
      case 'tar':
      case 'gz':
        return 'zip';
      default:
        return 'other';
    }
  };

  // 构造附件列表的通用函数
  const buildAttachmentList = (bizType: string, materials: any) => {
    console.log('构造附件列表:', materials);
    const attachmentList: any[] = [];

    // 处理图片附件
    if (materials.images) {
      try {
        const images = typeof materials.images === 'string' ? JSON.parse(materials.images) : materials.images;
        if (Array.isArray(images)) {
          attachmentList.push(
            ...images.map((file: any) => ({
              bizType,
              fileName: file.fileName,
              filePath: file.filePath,
              fileSize: file.fileSize,
              fileType: getFileTypeByExtension(file.filePath),
            }))
          );
        }
      } catch (e) {
        console.warn('解析图片附件数据失败:', e);
      }
    }

    // 处理其他附件
    if (materials.attachments) {
      try {
        const attachments = typeof materials.attachments === 'string' ? JSON.parse(materials.attachments) : materials.attachments;
        if (Array.isArray(attachments)) {
          attachmentList.push(
            ...attachments.map((file: any) => ({
              bizType,
              fileName: file.fileName,
              filePath: file.filePath,
              fileSize: file.fileSize,
              fileType: getFileTypeByExtension(file.filePath),
            }))
          );
        }
      } catch (e) {
        console.warn('解析其他附件数据失败:', e);
      }
    }

    return attachmentList;
  };

  // 获取封面图片路径的函数
  const getCoverImage = (stepData: any): string => {
    // 优先从 other.coverImage 字段获取封面图片（现在存储的是 URL 字符串）
    if (stepData.other && stepData.other.coverImage && typeof stepData.other.coverImage === 'string') {
      return stepData.other.coverImage;
    }

    // 如果没有专门的封面图片，则从 materials.images 中获取第一张图片作为封面图片
    if (stepData.materials && stepData.materials.images) {
      try {
        const images = typeof stepData.materials.images === 'string' ? JSON.parse(stepData.materials.images) : stepData.materials.images;
        if (Array.isArray(images) && images.length > 0) {
          return images[0].filePath || '';
        }
      } catch (e) {
        console.warn('解析图片数据失败:', e);
      }
    }
    return '';
  };

  // 重置表单数据
  const resetFormData = () => {
    // 重置第一步数据
    stepOneData.entrustOrderId = '';
    stepOneData.serviceType = 2;
    serviceType.value = 2;

    // 重置委托信息
    stepOneData.entrustInfo = {
      title: '',
      type: '',
      description: '',
      noticeName: '',
      onEntrustCompanyId: '',
      entrustCompanyId: '',
    };

    // 重置基本信息
    stepOneData.basicInfo = {
      entrustCompanyId: '',
      onEntrustCompanyId: '',
      assetName: '',
      assetNo: '',
      assetType: [] as string[],
      quantity: '',
      unit: '台',
      quantityFlag: 0,
      serviceLife: 0,
      depreciationDegree: 1,
      currentStatus: 1,
      appraisalValue: 0,
      disposalPrice: 0,
      disposalStartTime: '',
      disposalEndTime: '',
      paymentMethod: 1,
      isTaxIncluded: '0',
      taxRate: 0, // 税点
      servicePayType: 1, // 支付方式，默认买方支付
    };

    // 重置地址信息
    stepOneData.location = {
      province: '110000',
      city: '110100',
      area: '110101',
      detailAddress: '',
      coordinates: {
        latitude: '',
        longitude: '',
      },
    };

    // 重置材料信息
    stepOneData.materials = {
      images: '',
      attachments: '',
      specialNote: '',
    };

    // 重置其他信息
    stepOneData.other = {
      images: '',
      attachments: '',
      entrustDocument: [],
      specialNote: '',
      coverImage: '',
    };

    // 重置附件列表
    stepOneData.hgyAttachmentList = [];

    // 重置第二步数据
    stepTwoData.contact = {
      contactName: '',
      contactPhone: '',
    };

    // 重置委托单列表
    entrustOrderList.value = [];

    console.log('表单数据已重置');
  };

  // 提交表单
  const submitForm = async (status: number) => {
    // 验证第二步表单数据
    const isStep2Valid = await step2Ref.value?.validateForm();
    if (!isStep2Valid) {
      return;
    }

    submitting.value = true;

    try {
      const contactData = {
        contactName: stepTwoData.contact.contactName,
        contactPhone: stepTwoData.contact.contactPhone,
      };

      let apiData: PlatformUpdateParams;

      if (serviceType.value === 2) {
        // 发布资产处置
        const basicData = stepOneData.basicInfo;

        apiData = {
          id: isEditMode.value ? editId.value : undefined,
          entrustOrderId: stepOneData.entrustOrderId, // 关联委托单号
          entrustType: 1, // 委托类型(1-增值 2-自主) - 管理中心是增值
          serviceType: 2, // 服务类型(2-资产处置)
          status: 3, // 状态(1-草稿 2-提交)
          entrustCompanyId: stepOneData.basicInfo.entrustCompanyId,
          onEntrustCompanyId: stepOneData.basicInfo.onEntrustCompanyId,
          assetName: basicData.assetName,
          assetNo: basicData.assetNo,
          // 将数组格式的资产类型转换为分级字段
          assetTypeOne: Array.isArray(basicData.assetType) && basicData.assetType.length > 0 ? basicData.assetType[0] : '',
          assetTypeTwo: Array.isArray(basicData.assetType) && basicData.assetType.length > 1 ? basicData.assetType[1] : '',
          assetTypeThree: Array.isArray(basicData.assetType) && basicData.assetType.length > 2 ? basicData.assetType[2] : '',
          quantity: basicData.quantity,
          unit: basicData.unit,
          quantityFlag: basicData.quantityFlag,
          servicePayType: basicData.servicePayType, // 支付方式 1-买方支付 2-卖方支付
          serviceLife: Number(basicData.serviceLife),
          depreciationDegree: basicData.depreciationDegree,
          currentStatus: basicData.currentStatus,
          appraisalValue: Number(basicData.appraisalValue),
          disposalPrice: Number(basicData.disposalPrice),
          disposalStartTime: basicData.disposalStartTime,
          disposalEndTime: basicData.disposalEndTime,
          paymentMethod: basicData.paymentMethod,
          isTaxIncluded: basicData.isTaxIncluded,
          provinceCode: stepOneData.location.province,
          cityCode: stepOneData.location.city,
          districtCode: stepOneData.location.area,
          address: stepOneData.location.detailAddress,
          specialNotes: stepOneData.materials.specialNote,
          relationUser: contactData.contactName,
          relationPhone: contactData.contactPhone,
          coverImage: getCoverImage(stepOneData),
          attachmentList: buildAttachmentList('WTCZ', stepOneData.materials),
        };
      } else if (serviceType.value === 3) {
        // 发布采购信息
        apiData = {
          id: isEditMode.value ? editId.value : undefined,
          entrustOrderId: stepOneData.entrustOrderId, // 关联委托单号
          entrustType: 1, // 委托类型(1-增值 2-自主) - 管理中心是增值
          serviceType: 3, // 服务类型(3-采购信息)
          status: 3, // 状态(1-草稿 2-提交)
          entrustCompanyId: stepOneData.entrustInfo.entrustCompanyId,
          onEntrustCompanyId: stepOneData.entrustInfo.onEntrustCompanyId,
          noticeName: stepOneData.entrustInfo.noticeName,
          provinceCode: stepOneData.location.province,
          cityCode: stepOneData.location.city,
          districtCode: stepOneData.location.area,
          address: stepOneData.location.detailAddress,
          relationUser: contactData.contactName,
          relationPhone: contactData.contactPhone,
          attachmentList: buildAttachmentList('WTCG', stepOneData.materials),
        };
      } else {
        throw new Error('不支持的服务类型');
      }

      // 根据服务类型调用不同的接口
      let result: any;
      if (serviceType.value === 2) {
        // 资产处置使用原接口
        result = await platformUpdate(apiData);
      } else if (serviceType.value === 3) {
        // 采购信息使用新接口
        result = await platformUpdateProcurement(apiData);
      } else {
        throw new Error('不支持的服务类型: ' + serviceType.value);
      }

      if (result) {
        // 发布成功，重置表单数据并回到首页面
        resetFormData();
        // 重置步骤到第一步
        currentStep.value = 0;
        // 重新获取委托单列表
        await fetchEntrustOrderList();
      }
    } catch (error) {
      console.error('提交失败:', error);
    } finally {
      submitting.value = false;
    }
  };
</script>

<style lang="less" scoped>
  // 主容器样式
  .appreciation-publish {
    padding: 24px;
    background-color: #fff;
    height: calc(100vh - 168px); // 减去头部和底部的高度
    overflow-y: auto;
    margin: 19px 30px 30px 30px;
    border-radius: 10px;
  }

  // 步骤条包装器
  .steps-wrapper {
    margin-bottom: 32px;
  }

  // 步骤内容
  .step-content {
    margin: 0 auto; // 居中显示
  }

  // 步骤面板
  .step-panel {
    background-color: #fff;
    border-radius: 8px;
  }

  // 操作按钮区域
  .step-actions {
    display: flex;
    justify-content: center;
    gap: 16px; // 按钮之间的间距
    margin-top: 48px; // 与表单内容的间距
    padding-top: 24px;

    .next-btn,
    .submit-btn,
    .draft-btn {
      width: 434px; // 按钮最小宽度
      height: 48px; // 按钮高度
      font-size: 16px; // 字体大小
      border-radius: 6px; // 圆角
    }

    .next-btn,
    .submit-btn {
      background-color: #004c66; // 主色调
      border-color: #004c66;

      &:hover:not(:disabled) {
        background: rgba(0, 76, 102, 0.9);
      }
    }

    .draft-btn {
      background-color: #fff;
      color: #666;
      border-color: #d9d9d9;

      &:hover {
        color: #004c66;
        border-color: #004c66;
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .step-content {
      max-width: 100%;
      padding: 0 16px;
    }

    .step-panel {
      padding: 24px 16px;
    }
  }

  @media (max-width: 768px) {
    .appreciation-publish {
      padding: 16px;
    }

    .steps-wrapper {
      padding: 0 8px;
      margin-bottom: 24px;
    }

    .step-content {
      padding: 0 8px;
    }

    .step-panel {
      padding: 16px;
      border-radius: 4px;
    }

    .step-actions {
      flex-direction: column; // 小屏幕下垂直排列
      gap: 12px;
      margin-top: 32px;

      .next-btn,
      .submit-btn,
      .draft-btn {
        width: 100%; // 小屏幕下占满宽度
      }
    }
  }
  :deep(.ant-table-title) {
    margin-top: 12px;
    border-top: 1px solid #ddd;
    padding: 0 !important;
    padding-top: 15px !important;
    margin-right: -8px;
  }
  /* :deep(.ant-form-item-row) {
    display: flex;
    justify-content: end;
  } */
  :deep(.ant-form-item-control-input-content) {
    /* display: flex;
    justify-content: end; */
    button {
      margin-right: 0;
      margin-left: 8px;
      box-shadow: 0 0 0 rgba(3, 38, 43, 0.42);
    }
  }
</style>
