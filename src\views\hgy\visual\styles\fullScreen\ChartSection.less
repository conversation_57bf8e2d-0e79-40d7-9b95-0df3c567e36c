// 全屏状态下的样式 - 1658px × 994px
.visual-container.fullscreen .chart-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
}

.visual-container.fullscreen .chart-container {
  position: relative;
  flex: 1;
  border-radius: 8px;
  overflow: hidden;
  min-height: 300px;
}

.visual-container.fullscreen .chart-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/center-bg_1754987648914.png') no-repeat center center;
  background-size: cover;
  z-index: 1;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(43, 204, 255, 0.05) 0%, rgba(43, 204, 255, 0.02) 50%, rgba(43, 204, 255, 0.05) 100%);
    border: 1px solid rgba(43, 204, 255, 0.2);
    border-radius: 8px;
  }
}

.visual-container.fullscreen .chart-content {
  position: relative;
  height: 100%;
  padding: 20px;
  z-index: 2;
}

.visual-container.fullscreen .chart-title {
  color: #ffffff;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 16px;
  text-align: center;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.visual-container.fullscreen .chart-wrapper {
  height: calc(100% - 50px);
}

.chart {
  width: 100%;
  height: 100%;
}
